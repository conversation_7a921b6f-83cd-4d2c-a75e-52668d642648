<?php
header('Content-Type: application/json');

// Database configuration
$host = 'localhost';
$dbname = 'group4_db';
$username = 'root';
$password = '';

try {
    // Create database connection
    $conn = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get all products from the database
    $stmt = $conn->prepare("SELECT * FROM products");
    $stmt->execute();
    
    // Fetch all products
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Return as JSON with additional info
    echo json_encode([
        'success' => true,
        'count' => count($products),
        'products' => $products,
        'query' => "SELECT * FROM products"
    ]);
    
} catch(PDOException $e) {
    // Return error as JSON
    echo json_encode([
        'success' => false,
        'message' => "Database error: " . $e->getMessage()
    ]);
}
?>