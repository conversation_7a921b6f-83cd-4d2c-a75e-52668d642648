document.addEventListener('DOMContentLoaded', function() {
    // Check if shop.js is already handling the products
    if (window.shopJsInitialized) {
        console.log('shop.js is already handling products, skipping display-products.js initialization');
        return;
    }
    
    const productsContainer = document.getElementById('productsGrid');
    
    if (!productsContainer) {
        console.error('Products container not found!');
        return;
    }
    
    productsContainer.innerHTML = '<div class="loading">Loading products...</div>';
    
    // Add a timestamp to prevent caching
    const timestamp = new Date().getTime();
    
    // Fetch products from the API
    fetch(`api/products.php?t=${timestamp}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Data received:', data);
            if (data.success) {
                displayProducts(data.products);
            } else {
                productsContainer.innerHTML = '<div class="error">Error: ' + data.message + '</div>';
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            productsContainer.innerHTML = '<div class="error">Error: ' + error.message + '</div>';
        });
    
    function displayProducts(products) {
        productsContainer.innerHTML = '';
        
        if (!products || products.length === 0) {
            productsContainer.innerHTML = '<div class="no-products">No products found</div>';
            return;
        }
        
        products.forEach(product => {
            const card = document.createElement('div');
            card.className = 'product-card';
            
            // Handle image path
            let imagePath;
            if (product.image && product.image.startsWith('http')) {
                imagePath = product.image;
            } else if (product.image) {
                imagePath = `img/products/${product.image}`;
            } else {
                imagePath = 'img/products/default-snack.png';
            }
            
            card.innerHTML = `
                <div class="product-image-container">
                    <img src="${imagePath}" onerror="this.src='img/products/default-snack.png'">
                </div>
                <div class="product-info">
                    <div class="product-name">${product.name}</div>
                    <div class="product-divider">─────────────</div>
                    <div class="product-description">${product.description}</div>
                </div>
                <div class="product-price">RM${parseFloat(product.price).toFixed(2)}</div>
            `;
            
            // Add click event to show product modal
            card.addEventListener('click', () => showProductModal(product.id));
            
            productsContainer.appendChild(card);
        });
    }
});








