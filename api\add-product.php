<?php
$conn = mysqli_connect('localhost', 'root', '', 'group4_db');

if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

if (isset($_POST['add_product'])) {
    $id = mysqli_real_escape_string($conn, $_POST['product_id']);
    $name = mysqli_real_escape_string($conn, $_POST['product_name']);
    $desc = mysqli_real_escape_string($conn, $_POST['product_desc']);
    $price = $_POST['product_price'];
    $ingredients = mysqli_real_escape_string($conn, $_POST['product_ingredients']);
    $nutrition = mysqli_real_escape_string($conn, $_POST['product_nutrition']);
    $category = mysqli_real_escape_string($conn, $_POST['product_category']);
    $stock = $_POST['product_stock'];

    $image_name = $_FILES['product_image']['name'];
    $image_tmp_name = $_FILES['product_image']['tmp_name'];
    $image_folder = __DIR__ . '/../img/products/' . $image_name;

    // Validation could go here (like checking for duplicate ID, empty fields, etc.)

    $insert_query = mysqli_query($conn, "INSERT INTO `products` 
    (`id`, `name`, `description`, `price`, `image`, `ingredients`, `nutrition`, `category`, `stock_quantity`) 
    VALUES 
    ('$id', '$name', '$desc', '$price', '$image_name', '$ingredients', '$nutrition', '$category', '$stock')") 
    or die("Insert query failed: " . mysqli_error($conn));

    if ($insert_query) {
        move_uploaded_file($image_tmp_name, $image_folder);
        $display_message = "Product inserted successfully.";
    } else {
        $display_message = "Error inserting product.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="images/favicon.jpg">
    <title>SnackAtlas | Add Products</title>

    <link rel="stylesheet" href="../css/add_product.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
</head>
<body>
    <div class="bg-image"></div>

    <header class="header">
        <div class="header_body">
            <a href="../add-product.php" class="logo">SnackAtlas</a>
        </div>
    </header>
    
    <div class="container">
        <?php
            if(isset($display_message))
            {
                echo "<div class='display_message'>
                <span>$display_message</span>
                <i class='fas fa-times' onclick = 'this.parentElement.style.display=`none`';></i>
                </div>";
            }
        ?>

        <section>
            <h3 class="heading">Add Products</h3>

            <form action="" class="add_product" method="post" enctype="multipart/form-data">
                <input type="text" name="product_id" placeholder="Enter product ID (e.g. P001)" class="input_fields" required>
                <input type="text" name="product_name" placeholder="Enter product name" class="input_fields" required>
                <textarea name="product_desc" placeholder="Enter product description" class="input_fields" required></textarea>
                <input type="number" name="product_price" min="0" placeholder="Enter product price ($)" class="input_fields" required>
                <textarea name="product_ingredients" placeholder="Enter ingredients" class="input_fields" required></textarea>
                <textarea name="product_nutrition" placeholder="Enter nutritional info" class="input_fields" required></textarea>
                <input type="text" name="product_category" placeholder="Enter product category" class="input_fields" required>
                <input type="number" name="product_stock" min="0" placeholder="Enter stock quantity" class="input_fields" required>
                <input type="file" name="product_image" class="input_fields" required accept="image/png, image/jpg, image/jpeg">
                <input type="submit" name="add_product" class="submit_btn" value="Add Product">
            </form>

        </section>
    </div>
</body>
</html>