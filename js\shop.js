// Global variables
let allProducts = []; // All products from the API
let filteredProducts = []; // Products after search/filter
let currentPage = 1;
let totalPages = 1;
let productsPerPage = 9;
let currentSearch = '';
let currentFilter = '';

// Real-time clock functionality
function updateClock() {
  const now = new Date();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  const ampm = hours >= 12 ? 'PM' : 'AM';
  const displayHours = hours % 12 || 12;
  const displayMinutes = minutes.toString().padStart(2, '0');
  
  const timeString = `${displayHours}:${displayMinutes} ${ampm}`;
  document.getElementById('clock').textContent = timeString;
}

// Update clock immediately and then every second
updateClock();
setInterval(updateClock, 1000);

// Load all products from API
async function loadAllProducts() {
    console.log('Loading all products...');
    
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) loadingIndicator.style.display = 'flex';
    
    try {
        // Fetch all products at once
        const response = await fetch('api/all-products.php?t=' + new Date().getTime());
        
        if (!response.ok) {
            throw new Error(`Server responded with status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.success) {
            allProducts = data.products || [];
            console.log(`Loaded ${allProducts.length} products`);
            
            // Initialize filtered products with all products
            filteredProducts = [...allProducts];
            
            // Calculate total pages
            updateTotalPages();
            
            // Display current page
            displayCurrentPage();
            
            // Update pagination UI
            updatePaginationUI();
        } else {
            console.error('API Error:', data.message);
            displaySampleProducts();
        }
    } catch (error) {
        console.error('Error loading products:', error);
        displaySampleProducts();
    } finally {
        if (loadingIndicator) loadingIndicator.style.display = 'none';
    }
}

// Update total pages based on filtered products
function updateTotalPages() {
    totalPages = Math.ceil(filteredProducts.length / productsPerPage);
    if (totalPages === 0) totalPages = 1; // At least one page even if empty
    
    // Adjust current page if needed
    if (currentPage > totalPages) {
        currentPage = totalPages;
    }
}

// Display current page of products
function displayCurrentPage() {
    const productsGrid = document.getElementById('productsGrid');
    if (!productsGrid) return;
    
    // Clear previous products
    productsGrid.innerHTML = '';
    
    // Calculate start and end indices for current page
    const startIndex = (currentPage - 1) * productsPerPage;
    const endIndex = Math.min(startIndex + productsPerPage, filteredProducts.length);
    
    console.log(`Displaying products ${startIndex + 1} to ${endIndex} of ${filteredProducts.length}`);
    
    // Get products for current page
    const currentPageProducts = filteredProducts.slice(startIndex, endIndex);
    
    if (currentPageProducts.length === 0) {
        productsGrid.innerHTML = '<div class="no-products">No products found</div>';
        return;
    }
    
    // Display products
    currentPageProducts.forEach(product => {
        const productCard = document.createElement('div');
        productCard.className = 'product-card';
        
        // Handle image path
        let imagePath;
        if (product.image && product.image.startsWith('http')) {
            imagePath = product.image;
        } else if (product.image) {
            imagePath = `img/products/${product.image}`;
        } else {
            imagePath = 'img/products/default-snack.png';
        }
        
        productCard.innerHTML = `
            <div class="product-image-container">
                <img src="${imagePath}" alt="${product.name}" onerror="this.src='img/products/default-snack.png'">
                <button class="heart-btn" onclick="toggleWishlist('${product.id}', event)" data-product-id="${product.id}">
                    <svg class="heart-icon" viewBox="0 0 24 24">
                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                    </svg>
                </button>
            </div>
            <div class="product-info">
                <div class="product-name">${product.name}</div>
                <div class="product-divider">─────────────</div>
                <div class="product-description">${product.description}</div>
            </div>
            <div class="product-price">RM${parseFloat(product.price || 0).toFixed(2)}</div>
        `;
        
        productCard.addEventListener('click', () => showProductModal(product.id));
        productsGrid.appendChild(productCard);
    });

    // Update wishlist UI after products are displayed
    updateWishlistUI();
}

// Apply search and filter to products
function applySearchAndFilter() {
    console.log(`Applying search: "${currentSearch}" and filter: "${currentFilter}"`);
    
    // Start with all products
    filteredProducts = [...allProducts];
    
    // Apply search if provided
    if (currentSearch) {
        const searchLower = currentSearch.toLowerCase();
        filteredProducts = filteredProducts.filter(product => 
            product.name.toLowerCase().includes(searchLower) || 
            product.description.toLowerCase().includes(searchLower)
        );
    }
    
    // Apply filter if provided
    if (currentFilter) {
        switch (currentFilter) {
            case 'name_asc':
                filteredProducts.sort((a, b) => a.name.localeCompare(b.name));
                break;
            case 'name_desc':
                filteredProducts.sort((a, b) => b.name.localeCompare(a.name));
                break;
            case 'price_asc':
                filteredProducts.sort((a, b) => parseFloat(a.price) - parseFloat(b.price));
                break;
            case 'price_desc':
                filteredProducts.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));
                break;
            default:
                // If filter is a category
                filteredProducts = filteredProducts.filter(product => 
                    product.category === currentFilter
                );
                break;
        }
    }
    
    console.log(`Found ${filteredProducts.length} products after filtering`);
    
    // Reset to first page
    currentPage = 1;
    
    // Update total pages
    updateTotalPages();
    
    // Display current page
    displayCurrentPage();
    
    // Update pagination UI
    updatePaginationUI();
}

// Setup search functionality
function setupSearch() {
    const searchInput = document.getElementById('searchInput');
    if (!searchInput) return;
    
    // Add input event listener
    searchInput.addEventListener('input', function() {
        currentSearch = this.value.trim();
        applySearchAndFilter();
    });
    
    // Add keydown event for Enter key
    searchInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            currentSearch = this.value.trim();
            applySearchAndFilter();
        }
    });
}

// Setup filter functionality
function setupFilters() {
    const filterSelect = document.getElementById('filterSelect');
    if (!filterSelect) return;
    
    filterSelect.addEventListener('change', function() {
        currentFilter = this.value;
        applySearchAndFilter();
    });
}

// Update pagination UI
function updatePaginationUI() {
    const pageNumbers = document.getElementById('pageNumbers');
    const prevArrow = document.getElementById('prevArrow');
    const nextArrow = document.getElementById('nextArrow');
    
    if (!pageNumbers || !prevArrow || !nextArrow) return;
    
    // Update arrows
    prevArrow.disabled = currentPage <= 1;
    nextArrow.disabled = currentPage >= totalPages;
    
    // Clear previous page numbers
    pageNumbers.innerHTML = '';
    
    // Generate page numbers
    for (let i = 1; i <= totalPages; i++) {
        const pageNumber = document.createElement('div');
        pageNumber.className = 'page-number';
        if (i === currentPage) pageNumber.classList.add('active');
        pageNumber.textContent = i;
        pageNumber.addEventListener('click', () => goToPage(i));
        pageNumbers.appendChild(pageNumber);
    }
    
    console.log(`Pagination updated: Page ${currentPage} of ${totalPages}`);
}

// Go to specific page
function goToPage(page) {
    console.log(`Going to page ${page}`);
    
    // Validate page number
    page = parseInt(page);
    if (isNaN(page) || page < 1 || page > totalPages) return;
    
    // Update current page
    currentPage = page;
    
    // Display current page
    displayCurrentPage();
    
    // Update pagination UI
    updatePaginationUI();
    
    // Scroll to top of products section
    const productsSection = document.querySelector('.products-section');
    if (productsSection) {
        productsSection.scrollIntoView({ behavior: 'smooth' });
    }
}

// Setup pagination
function setupPagination() {
    const prevArrow = document.getElementById('prevArrow');
    const nextArrow = document.getElementById('nextArrow');
    
    if (prevArrow) {
        prevArrow.addEventListener('click', () => {
            if (currentPage > 1) goToPage(currentPage - 1);
        });
    }
    
    if (nextArrow) {
        nextArrow.addEventListener('click', () => {
            if (currentPage < totalPages) goToPage(currentPage + 1);
        });
    }
}

// Product Modal Functions
let currentQuantity = 1;

// Show product modal with details
async function showProductModal(productId) {
    console.log('Opening modal for product:', productId);

    try {
        // Find product in allProducts array
        const product = allProducts.find(p => p.id === productId);

        if (!product) {
            console.error('Product not found:', productId);
            return;
        }

        // Update modal content
        updateModalContent(product);

        // Show modal
        const modal = document.getElementById('productModal');
        if (modal) {
            modal.classList.add('show');
            document.body.style.overflow = 'hidden';
        }

    } catch (error) {
        console.error('Error showing product modal:', error);
    }
}

// Update modal content with product details
function updateModalContent(product) {
    // Handle image path
    let imagePath;
    if (product.image && product.image.startsWith('http')) {
        imagePath = product.image;
    } else if (product.image) {
        imagePath = `img/products/${product.image}`;
    } else {
        imagePath = 'img/products/default-snack.png';
    }

    // Update modal elements
    const modalImage = document.getElementById('modalImage');
    const modalName = document.getElementById('modalName');
    const modalPrice = document.getElementById('modalPrice');
    const modalStatus = document.getElementById('modalStatus');
    const modalIngredients = document.getElementById('modalIngredients');
    const modalNutrition = document.getElementById('modalNutrition');
    const quantityDisplay = document.getElementById('quantity');

    if (modalImage) {
        modalImage.src = imagePath;
        modalImage.alt = product.name;
        modalImage.setAttribute('data-id', product.id);
        modalImage.onerror = function() { this.src = 'img/products/default-snack.png'; };
    }

    if (modalName) modalName.textContent = product.name || 'Product Name';
    if (modalPrice) modalPrice.textContent = `RM${parseFloat(product.price || 0).toFixed(2)}`;

    // Update status based on stock
    if (modalStatus) {
        const stockQuantity = parseInt(product.stock_quantity || 0);
        if (stockQuantity > 0) {
            modalStatus.textContent = 'In Stock';
            modalStatus.className = 'status-badge';
        } else {
            modalStatus.textContent = 'Out of Stock';
            modalStatus.className = 'status-badge unavailable';
        }
    }

    if (modalIngredients) {
        modalIngredients.textContent = product.ingredients || 'No ingredients listed';
    }

    if (modalNutrition) {
        modalNutrition.textContent = product.nutrition || 'No nutrition info available';
    }

    // Reset quantity
    currentQuantity = 1;
    if (quantityDisplay) quantityDisplay.textContent = currentQuantity;
}

// Close modal
function closeModal() {
    const modal = document.getElementById('productModal');
    if (modal) {
        modal.classList.remove('show');
        document.body.style.overflow = 'auto';
    }

    // Reset quantity
    currentQuantity = 1;
    const quantityDisplay = document.getElementById('quantity');
    if (quantityDisplay) quantityDisplay.textContent = currentQuantity;
}

// Change quantity in modal
function changeQuantity(change) {
    const newQuantity = currentQuantity + change;

    // Minimum quantity is 1
    if (newQuantity < 1) return;

    currentQuantity = newQuantity;
    const quantityDisplay = document.getElementById('quantity');
    if (quantityDisplay) {
        quantityDisplay.textContent = currentQuantity;
    }
}

// Add to cart function
async function addToCart() {
    const modalImage = document.getElementById('modalImage');
    const productId = modalImage ? modalImage.getAttribute('data-id') : null;

    if (!productId) {
        showNotification('Error: Product not found', 'error');
        return;
    }

    try {
        const response = await fetch('api/save-to-cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                product_id: productId,
                quantity: currentQuantity
            })
        });

        const data = await response.json();

        if (data.success) {
            showNotification(`Added ${currentQuantity} item(s) to cart!`, 'success');
            closeModal();
        } else {
            showNotification('Failed to add to cart: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('Error adding to cart:', error);
        showNotification('Error adding to cart', 'error');
    }
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        animation: slideIn 0.3s ease-out;
    `;

    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);

    // Add to page
    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-out';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Close modal when clicking outside or on backdrop
document.addEventListener('click', function(e) {
    const modal = document.getElementById('productModal');
    const modalBackdrop = document.querySelector('.modal-backdrop');

    if (modal && (e.target === modal || e.target === modalBackdrop)) {
        closeModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
    }
});

// Wishlist functionality
let userWishlist = new Set();

// Load user's wishlist
async function loadUserWishlist() {
    try {
        const currentUser = localStorage.getItem('currentSnackAtlasUser');
        if (!currentUser) return;

        const user = JSON.parse(currentUser);
        const response = await fetch(`api/get_wishlist.php?user_id=${user.id}`);
        const result = await response.json();

        if (result.success) {
            userWishlist = new Set(result.wishlist.map(item => item.product_id));
            updateWishlistUI();
        }
    } catch (error) {
        console.error('Error loading wishlist:', error);
    }
}

// Toggle wishlist status
async function toggleWishlist(productId, event) {
    event.stopPropagation(); // Prevent opening product modal

    try {
        const currentUser = localStorage.getItem('currentSnackAtlasUser');
        if (!currentUser) {
            showNotification('Please login to save items to wishlist', 'error');
            return;
        }

        const user = JSON.parse(currentUser);
        const isLiked = userWishlist.has(productId);

        const response = await fetch('api/toggle_wishlist.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                user_id: user.id,
                product_id: productId,
                action: isLiked ? 'remove' : 'add'
            })
        });

        const result = await response.json();

        if (result.success) {
            if (isLiked) {
                userWishlist.delete(productId);
                showNotification('Removed from wishlist', 'success');
            } else {
                userWishlist.add(productId);
                showNotification('Added to wishlist', 'success');
            }
            updateWishlistUI();
        } else {
            showNotification('Failed to update wishlist: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('Error toggling wishlist:', error);
        showNotification('Error updating wishlist', 'error');
    }
}

// Update wishlist UI
function updateWishlistUI() {
    document.querySelectorAll('.heart-btn').forEach(btn => {
        const productId = btn.getAttribute('data-product-id');
        const heartIcon = btn.querySelector('.heart-icon');

        if (userWishlist.has(productId)) {
            heartIcon.classList.add('liked');
        } else {
            heartIcon.classList.remove('liked');
        }
    });
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded');

    // Setup search functionality
    setupSearch();

    // Setup filter functionality
    setupFilters();

    // Setup pagination
    setupPagination();

    // Load all products
    loadAllProducts();

    // Load user's wishlist
    loadUserWishlist();
});

// Notification function
function showNotification(message, type = 'info') {
    // Remove existing notification
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;

    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 10px;
        font-family: 'Poppins', sans-serif;
        font-weight: 600;
        z-index: 2000;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        animation: slideInRight 0.3s ease-out;
    `;

    // Add animation styles if not already present
    if (!document.getElementById('notificationStyles')) {
        const style = document.createElement('style');
        style.id = 'notificationStyles';
        style.textContent = `
            @keyframes slideInRight {
                from {
                    opacity: 0;
                    transform: translateX(100px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }

            @keyframes slideOutRight {
                from {
                    opacity: 1;
                    transform: translateX(0);
                }
                to {
                    opacity: 0;
                    transform: translateX(100px);
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOutRight 0.3s ease-out';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }
    }, 3000);
}
