* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  background-color: white;
  overflow-x: hidden;
}

.bg-image {
  position: absolute;
  width: 100vw;
  height: auto;
  object-fit: cover;
  z-index: 1;
}

.bg-1 {
  top: 0;
  left: 0;
  min-height: 100vh;
}

.app {
  position: relative;
  min-height: 100vh;
}

.page {
  display: none;
}

.page.active {
  display: block;
}

.profile-container {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
}

.header {
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2.5rem;
}

.logo {
  width: 53px;
  height: 49px;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.clock {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  color: #582f0e;
  font-size: 24px;
  letter-spacing: 0.24px;
  min-width: 120px;
  text-align: center;
}

.time {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  color: #582f0e;
  font-size: 28px;
  letter-spacing: 0.28px;
}

.main-content {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 1rem;
  flex: 1;
}

.page-title {
  font-family: 'Playfair Display', serif;
  font-weight: 800;
  color: #e6ccb2;
  font-size: 100px;
  text-align: center;
  letter-spacing: 0;
  -webkit-text-stroke: 1.5px #000000;
  margin-bottom: 4rem;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 5rem;
  width: 100%;
  max-width: 1200px;
}

.profile-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar-container {
  position: relative;
  width: 330px;
  height: 324px;
  margin-bottom: 2rem;
}

.profile-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3px solid #582f0e;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3px solid #582f0e;
  background-color: #cfcfcf;
  opacity: 0.7;
  filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.avatar-placeholder span {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  color: #e6ccb2;
  font-size: 24px;
  -webkit-text-stroke: 1px #000000;
  letter-spacing: 0.45px;
}

.profile-info {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  color: #e6ccb2;
  font-size: 24px;
  text-align: center;
  -webkit-text-stroke: 1px #000000;
  letter-spacing: 0.45px;
  line-height: 1.5;
}

.profile-name {
  margin-bottom: 1rem;
}

.menu-section {
  background: transparent;
}

.menu-list {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.menu-item:hover {
  transform: translateX(10px);
}

.menu-icon {
  font-size: 24px;
}

.menu-text {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  color: #e6ccb2;
  font-size: 24px;
  -webkit-text-stroke: 1px #000000;
  text-stroke: 1.5px #000000;
  letter-spacing: 0.45px;
}

.footer {
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: center;
  margin-top: auto;
  padding: 2.5rem 0;
}

.back-button {
  background: none;
  border: none;
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  color: #ede0d4;
  font-size: 26px;
  -webkit-text-stroke: 1px #000000;
  letter-spacing: 0.52px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: transform 0.2s ease;
}

.back-button:hover {
  transform: translateX(-5px);
}

.back-icon {
  width: 20px;
  height: 20px;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

.modal.active {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: linear-gradient(135deg, #f5f1eb 0%, #e8ddd4 100%);
  border-radius: 20px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem 2rem;
  border-bottom: 2px solid #d4c4b0;
}

.modal-header h2 {
  font-family: 'Playfair Display', serif;
  font-weight: 800;
  color: #582f0e;
  font-size: 28px;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  font-size: 32px;
  color: #582f0e;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.close-button:hover {
  background-color: rgba(88, 47, 14, 0.1);
}

.modal-body {
  padding: 2rem;
}

.form-group {
  margin-bottom: 2rem;
}

.form-label {
  display: block;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  color: #582f0e;
  font-size: 16px;
  margin-bottom: 0.75rem;
}

.image-upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.current-image {
  position: relative;
  width: 120px;
  height: 120px;
}

.modal-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3px solid #582f0e;
  object-fit: cover;
}

.modal-avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3px solid #582f0e;
  background-color: #d4c4b0;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.modal-avatar-placeholder span {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  color: #582f0e;
  font-size: 14px;
}

.upload-controls {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.upload-button,
.remove-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.upload-button {
  background-color: #582f0e;
  color: white;
}

.upload-button:hover {
  background-color: #6d3a12;
  transform: translateY(-2px);
}

.remove-button {
  background-color: #dc3545;
  color: white;
}

.remove-button:hover {
  background-color: #c82333;
  transform: translateY(-2px);
}

.form-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #d4c4b0;
  border-radius: 10px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #582f0e;
  background-color: white;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: #582f0e;
}

.email-display {
  padding: 1rem;
  background-color: #f8f6f3;
  border: 2px solid #e8ddd4;
  border-radius: 10px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #888;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1rem 2rem 2rem 2rem;
  border-top: 2px solid #d4c4b0;
}

.cancel-button,
.save-button {
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 10px;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button {
  background-color: #6c757d;
  color: white;
}

.cancel-button:hover {
  background-color: #5a6268;
  transform: translateY(-2px);
}

.save-button {
  background-color: #28a745;
  color: white;
}

.save-button:hover {
  background-color: #218838;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (min-width: 768px) {
  .content-wrapper {
    flex-direction: row;
    align-items: flex-start;
  }
  
  .page-title {
    font-size: 100px;
  }
}

@media (max-width: 767px) {
  .page-title {
    font-size: 60px;
  }
  
  .header {
    padding: 1.5rem;
  }
  
  .time {
    font-size: 20px;
  }
  
  .menu-text,
  .profile-info {
    font-size: 20px;
  }
  
  .avatar-container {
    width: 250px;
    height: 250px;
  }
  
  .modal-content {
    width: 95%;
    margin: 1rem;
  }
  
  .modal-header,
  .modal-body {
    padding: 1.5rem;
  }
  
  .upload-controls {
    flex-direction: column;
    align-items: center;
  }
}

/* Wishlist Modal Styles */
.wishlist-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  max-height: 60vh;
  overflow-y: auto;
  padding: 10px;
}

.wishlist-item {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid #582f0e;
  border-radius: 15px;
  padding: 15px;
  display: flex;
  gap: 15px;
  transition: all 0.2s ease;
}

.wishlist-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(88, 47, 14, 0.2);
}

.wishlist-item-image {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 10px;
  overflow: hidden;
}

.wishlist-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.wishlist-item-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.wishlist-item-name {
  font-size: 16px;
  font-weight: 600;
  color: #582f0e;
  margin: 0;
}

.wishlist-item-description {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.wishlist-item-price {
  font-size: 16px;
  font-weight: 600;
  color: #8b4513;
}

.remove-wishlist-btn {
  background: #d32f2f;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
  align-self: flex-start;
}

.remove-wishlist-btn:hover {
  background: #b71c1c;
}

/* Delete Account Modal Styles */
.delete-button {
  background: #d32f2f !important;
  color: white !important;
  border: none !important;
  padding: 10px 20px !important;
  border-radius: 5px !important;
  cursor: pointer !important;
  font-weight: 500 !important;
  transition: background 0.2s ease !important;
}

.delete-button:hover {
  background: #b71c1c !important;
}

/* Responsive adjustments for wishlist */
@media (max-width: 768px) {
  .wishlist-grid {
    grid-template-columns: 1fr;
    max-height: 50vh;
  }

  .wishlist-item {
    flex-direction: column;
    text-align: center;
  }

  .wishlist-item-image {
    width: 100px;
    height: 100px;
    margin: 0 auto;
  }
}