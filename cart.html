<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SnackAtlas | Cart</title>

    <link rel="stylesheet" href="css/cart.css">
    <link rel="icon" type="image/x-icon" href="img/logo.png">
</head>
<body>
    <div class="container">
        <img class="bg-image bg-1" alt="Background" src="img/cart-bg.png" />

        <div class="content-wrapper">
            <!-- Header -->
            <header class="header">
                <div class="nav-left">
                  <a href="homepage.html"><img class="logo" alt="Logo" src="img/logo.png" /></a>
                </div>

                <nav class="nav-center" id="navCenter">
                  <ul class="nav-links">
                    <li><a href="shop.html" class="nav-link">← back to shop</a></li>
                  </ul>
                </nav>

                <div class="nav-right">
                    <div class="nav-icon">
                        <a href="profile.html">
                            <img src="img/user-icon.svg" alt="user icon">
                        </a>
                    </div>
                  <div class="clock" id="clock">12:00 PM</div>
                </div>
            </header>

            <!-- Page Title -->
            <h1 class="page-title">My Cart</h1>

             <div class="cart-content">
            <!-- Cart Items Section -->
            <div class="cart-items-section">
                <div class="cart-items" id="cartItems">
                    <!-- Cart Item 1 -->
                    <div class="cart-item" data-item-id="1">
                        <div class="item-image">
                            <img src="https://images.pexels.com/photos/894695/pexels-photo-894695.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop" alt="Premium Coffee Beans">
                        </div>
                        <div class="item-details">
                            <h3 class="item-name">Premium Coffee Beans</h3>
                            <p class="item-description">Organic Arabica coffee beans, medium roast</p>
                            <div class="item-price">RM <span class="price-value">14.99</span></div>
                        </div>
                        <div class="quantity-controls">
                            <button class="quantity-btn decrease" onclick="updateQuantity(1, -1)">−</button>
                            <span class="quantity-display" id="quantity-1">2</span>
                            <button class="quantity-btn increase" onclick="updateQuantity(1, 1)">+</button>
                        </div>
                        <div class="item-total">
                            RM <span class="total-value" id="total-1">29.98</span>
                        </div>
                        <button class="remove-btn" onclick="removeItem(1)">
                            <span>×</span>
                        </button>
                    </div>

                    <!-- Cart Item 2 -->
                    <div class="cart-item" data-item-id="2">
                        <div class="item-image">
                            <img src="https://images.pexels.com/photos/1638280/pexels-photo-1638280.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop" alt="Organic Tea Set">
                        </div>
                        <div class="item-details">
                            <h3 class="item-name">Organic Tea Set</h3>
                            <p class="item-description">Premium herbal tea collection with 6 varieties</p>
                            <div class="item-price">RM <span class="price-value">45.00</span></div>
                        </div>
                        <div class="quantity-controls">
                            <button class="quantity-btn decrease" onclick="updateQuantity(2, -1)">−</button>
                            <span class="quantity-display" id="quantity-2">1</span>
                            <button class="quantity-btn increase" onclick="updateQuantity(2, 1)">+</button>
                        </div>
                        <div class="item-total">
                            RM <span class="total-value" id="total-2">45.00</span>
                        </div>
                        <button class="remove-btn" onclick="removeItem(2)">
                            <span>×</span>
                        </button>
                    </div>

                    <!-- Cart Item 3 -->
                    <div class="cart-item" data-item-id="3">
                        <div class="item-image">
                            <img src="https://images.pexels.com/photos/6347888/pexels-photo-6347888.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop" alt="Ceramic Mug">
                        </div>
                        <div class="item-details">
                            <h3 class="item-name">Ceramic Mug</h3>
                            <p class="item-description">Handcrafted ceramic mug with elegant design</p>
                            <div class="item-price">RM <span class="price-value">11.99</span></div>
                        </div>
                        <div class="quantity-controls">
                            <button class="quantity-btn decrease" onclick="updateQuantity(3, -1)">−</button>
                            <span class="quantity-display" id="quantity-3">3</span>
                            <button class="quantity-btn increase" onclick="updateQuantity(3, 1)">+</button>
                        </div>
                        <div class="item-total">
                            RM <span class="total-value" id="total-3">35.97</span>
                        </div>
                        <button class="remove-btn" onclick="removeItem(3)">
                            <span>×</span>
                        </button>
                    </div>
                </div>

                <!-- Empty Cart Message -->
                <div class="empty-cart" id="emptyCart" style="display: none;">
                    <div class="empty-cart-icon">🛒</div>
                    <h3>Your cart is empty</h3>
                    <p>Add some items to get started!</p>
                    <button class="continue-shopping-btn" onclick="backToShop()">Continue Shopping</button>
                </div>
            </div>

            <!-- Cart Summary Section -->
            <div class="cart-summary-section" id="cartSummary">
                <div class="cart-summary">
                    <h2>Order Summary</h2>
                    
                    <div class="summary-row">
                        <span>Subtotal (<span id="itemCount">6</span> items):</span>
                        <span>RM <span id="subtotalAmount">110.95</span></span>
                    </div>
                    
                    <div class="summary-row">
                        <span>Shipping Fee:</span>
                        <span>RM <span id="shippingFee">5.00</span></span>
                    </div>
                    
                    <div class="summary-divider"></div>
                    
                    <div class="summary-row total-row">
                        <span>Grand Total:</span>
                        <span>RM <span id="grandTotal">115.95</span></span>
                    </div>
                    
                    <a href="checkout.html">
                        <button class="checkout-btn" onclick="proceedToCheckout()">
                            Proceed to Checkout
                        </button>
                    </a>
                </div>
            </div>


        </div>
      </div>
    </div>

    <script src="js/cart.js"></script>
</body>
</html>