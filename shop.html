<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>SnackAtlas | Shop</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <link rel="icon" type="image/x-icon" href="img/logo.png">
    <link href="css/shop.css" rel="stylesheet" />
  </head>
  <body>
    <div class="container">
      <!-- Background Images -->
      <img class="bg-image bg-1" alt="Background" src="img/homepage-bg.png" />

      <div class="content-wrapper">
            <!-- Header -->
            <header class="header">
                <div class="nav-left">
                  <img class="logo" alt="Logo" src="img/logo.png" />
                </div>
                
                <nav class="nav-center">
                  <ul class="nav-links">
                    <li><a href="homepage.html" class="nav-link">← back to home</a></li>
                  </ul>
                </nav>
                
                <div class="nav-right">
                    <div class="nav-icon">
                        <a href="cart.html">
                            <img src="img/cart-icon.svg" alt="cart icon">
                        </a>
                    </div>
                    <div class="nav-icon">
                        <a href="profile.html">  
                            <img src="img/user-icon.svg" alt="user icon">
                        </a>
                    </div>
                  <div class="clock" id="clock">12:00 PM</div>
                </div>
            </header>

            <!-- Search Bar -->
            <div class="search-container">
                <div class="search-bar">
                    <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.35-4.35"></path>
                    </svg>
                    <input type="text" id="searchInput" placeholder="Search for a product..." class="search-input">
                    <!-- Filter Dropdown -->
                    <div class="filter-section">
                        <img src="img/filter-icon.svg" alt="Filter" class="filter-icon">
                        <select id="filterSelect" class="filter-dropdown">
                            <option value="">All Products</option>
                            <option value="name_asc">Name A-Z</option>
                            <option value="name_desc">Name Z-A</option>
                            <option value="price_asc">Price Low to High</option>
                            <option value="price_desc">Price High to Low</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Page Title -->
            <h1 class="page-title">Shop</h1>

            <!-- Products Grid -->
            <div id="productsGrid" class="products-grid"></div>

            <!-- Loading Indicator -->
            <div id="loadingIndicator" class="loading">Loading products...</div>

            <!-- Pagination -->
            <div class="pagination" id="pagination">
                <button id="prevArrow" class="pagination-arrow">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="15,18 9,12 15,6"></polyline>
                    </svg>
                </button>
                <div id="pageNumbers" class="page-numbers">
                    <!-- Page numbers will be generated here -->
                </div>
                <button id="nextArrow" class="pagination-arrow">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="9,18 15,12 9,6"></polyline>
                    </svg>
                </button>
            </div>
      </div>
    </div>

     <!-- Product Modal -->
    <div id="productModal" class="modal">
        <div class="modal-backdrop"></div>
        <div class="modal-content">
            <button class="modal-close" onclick="closeModal()">×</button>
            <div class="modal-body">
                <div class="modal-image">
                    <img id="modalImage" data-id="" src="" alt="Product">
                </div>
                <div class="modal-details">
                    <h2 id="modalName" class="modal-product-name">Product name</h2>
                    <div class="modal-status">
                        <span>Status: </span>
                        <span id="modalStatus" class="status-badge">In Stock</span>
                    </div>
                    <div class="modal-price">
                        <span>Price: </span>
                        <span id="modalPrice">RM0.00</span>
                    </div>
                    
                    <div class="modal-section">
                        <h3>Ingredient list:</h3>
                        <p id="modalIngredients">No ingredients listed</p>
                    </div>
                    
                    <div class="modal-section">
                        <h3>Nutritional value:</h3>
                        <p id="modalNutrition">No nutrition info available</p>
                    </div>
                    
                    <div class="quantity-section">
                        <button class="quantity-btn" onclick="changeQuantity(-1)">−</button>
                        <span id="quantity" class="quantity">1</span>
                        <button class="quantity-btn" onclick="changeQuantity(1)">+</button>
                    </div>
                    
                    <button class="add-to-cart-btn" onclick="addToCart()">
                        🛒 ADD TO CART
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- User Profile Modal -->
    <div id="userModal" class="modal user-modal">
        <div class="modal-backdrop" onclick="closeUserModal()"></div>
        <div class="modal-content">
            <button class="modal-close" onclick="closeUserModal()">×</button>
            <div class="user-modal-body">
                <h2>User Profile</h2>
                
                <div class="user-tabs">
                    <button class="tab-btn active" onclick="showTab('profile')">Profile</button>
                    <button class="tab-btn" onclick="showTab('wishlist')">Wishlist</button>
                    <button class="tab-btn" onclick="showTab('orders')">Orders</button>
                    <button class="tab-btn" onclick="showTab('settings')">Settings</button>
                </div>
                
                <div id="profileTab" class="tab-content active">
                    <div class="profile-section">
                        <div class="profile-picture">
                            <img id="profileImage" src="https://images.pexels.com/photos/771742/pexels-photo-771742.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop" alt="Profile Picture">
                            <input type="file" id="profileImageInput" accept="image/*" style="display: none;">
                            <button onclick="document.getElementById('profileImageInput').click()" class="upload-btn">Upload Photo</button>
                        </div>
                        <div class="profile-info">
                            <div class="form-group">
                                <label>Username:</label>
                                <input type="text" id="username" value="John Doe" class="form-input">
                            </div>
                            <div class="form-group">
                                <label>Email:</label>
                                <input type="email" id="email" value="<EMAIL>" class="form-input">
                            </div>
                            <button onclick="updateProfile()" class="save-btn">Save Changes</button>
                        </div>
                    </div>
                </div>
                
                <div id="wishlistTab" class="tab-content">
                    <h3>Your Wishlist</h3>
                    <div id="wishlistProducts" class="saved-products-grid">
                        <!-- Wishlist products will be loaded here -->
                    </div>
                </div>
                
                <div id="ordersTab" class="tab-content">
                    <h3>Order History</h3>
                    <div id="orderHistory" class="order-history">
                        <!-- Order history will be loaded here -->
                    </div>
                </div>
                
                <div id="settingsTab" class="tab-content">
                    <h3>Account Settings</h3>
                    <div class="settings-section">
                        <button onclick="changePassword()" class="settings-btn">Change Password</button>
                        <button onclick="deleteAccount()" class="delete-btn">Delete Account</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/shop.js"></script>
    <script src="js/display-products.js"></script>
  </body>
</html>
