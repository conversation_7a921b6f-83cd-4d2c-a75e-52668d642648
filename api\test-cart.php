<?php
/**
 * Cart Functionality Test Script
 * This script tests the updated cart functionality with product images and prices
 */

header('Content-Type: application/json');

$host = 'localhost';
$dbname = 'group4_db';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $tests = [];
    $allPassed = true;
    
    // Test 1: Check table structure
    $tests[] = "Test 1: Checking cart table structure...";
    $stmt = $pdo->query("DESCRIBE cart");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $requiredColumns = ['id', 'product_id', 'product_name', 'product_image', 'product_price', 'quantity', 'added_at'];
    $foundColumns = array_column($columns, 'Field');
    
    $missingColumns = array_diff($requiredColumns, $foundColumns);
    if (empty($missingColumns)) {
        $tests[] = "✓ All required columns present";
    } else {
        $tests[] = "✗ Missing columns: " . implode(', ', $missingColumns);
        $allPassed = false;
    }
    
    // Test 2: Check if products table has data
    $tests[] = "Test 2: Checking products table...";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
    $productCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($productCount > 0) {
        $tests[] = "✓ Products table has $productCount products";
        
        // Get a sample product for testing
        $stmt = $pdo->query("SELECT id, name, price, image FROM products LIMIT 1");
        $sampleProduct = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($sampleProduct) {
            $tests[] = "✓ Sample product found: " . $sampleProduct['name'];
            
            // Test 3: Test adding product to cart
            $tests[] = "Test 3: Testing add to cart functionality...";
            
            // Clear any existing test data
            $pdo->prepare("DELETE FROM cart WHERE product_id = ?")->execute([$sampleProduct['id']]);
            
            // Add product to cart
            $stmt = $pdo->prepare("
                INSERT INTO cart (product_id, product_name, product_image, product_price, quantity) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $result = $stmt->execute([
                $sampleProduct['id'],
                $sampleProduct['name'],
                $sampleProduct['image'],
                $sampleProduct['price'],
                2
            ]);
            
            if ($result) {
                $tests[] = "✓ Product added to cart successfully";
                
                // Test 4: Test retrieving cart items
                $tests[] = "Test 4: Testing cart retrieval...";
                $stmt = $pdo->prepare("
                    SELECT 
                        product_id,
                        product_name,
                        product_image,
                        product_price,
                        quantity,
                        (product_price * quantity) as total_price
                    FROM cart 
                    WHERE product_id = ?
                ");
                $stmt->execute([$sampleProduct['id']]);
                $cartItem = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($cartItem) {
                    $tests[] = "✓ Cart item retrieved successfully";
                    $tests[] = "  - Product: " . $cartItem['product_name'];
                    $tests[] = "  - Price: RM " . number_format($cartItem['product_price'], 2);
                    $tests[] = "  - Quantity: " . $cartItem['quantity'];
                    $tests[] = "  - Total: RM " . number_format($cartItem['total_price'], 2);
                    $tests[] = "  - Image: " . ($cartItem['product_image'] ?: 'No image');
                    
                    // Test 5: Test updating quantity
                    $tests[] = "Test 5: Testing quantity update...";
                    $stmt = $pdo->prepare("UPDATE cart SET quantity = ? WHERE product_id = ?");
                    $result = $stmt->execute([5, $sampleProduct['id']]);
                    
                    if ($result) {
                        $tests[] = "✓ Quantity updated successfully";
                        
                        // Verify update
                        $stmt = $pdo->prepare("SELECT quantity FROM cart WHERE product_id = ?");
                        $stmt->execute([$sampleProduct['id']]);
                        $newQuantity = $stmt->fetch(PDO::FETCH_ASSOC)['quantity'];
                        
                        if ($newQuantity == 5) {
                            $tests[] = "✓ Quantity verification passed";
                        } else {
                            $tests[] = "✗ Quantity verification failed";
                            $allPassed = false;
                        }
                    } else {
                        $tests[] = "✗ Quantity update failed";
                        $allPassed = false;
                    }
                    
                    // Test 6: Test cart summary
                    $tests[] = "Test 6: Testing cart summary...";
                    $stmt = $pdo->query("
                        SELECT 
                            COUNT(*) as total_items,
                            SUM(quantity) as total_quantity,
                            SUM(product_price * quantity) as subtotal
                        FROM cart
                    ");
                    $summary = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    $tests[] = "✓ Cart summary calculated";
                    $tests[] = "  - Total items: " . $summary['total_items'];
                    $tests[] = "  - Total quantity: " . $summary['total_quantity'];
                    $tests[] = "  - Subtotal: RM " . number_format($summary['subtotal'], 2);
                    
                    // Clean up test data
                    $pdo->prepare("DELETE FROM cart WHERE product_id = ?")->execute([$sampleProduct['id']]);
                    $tests[] = "✓ Test data cleaned up";
                    
                } else {
                    $tests[] = "✗ Failed to retrieve cart item";
                    $allPassed = false;
                }
            } else {
                $tests[] = "✗ Failed to add product to cart";
                $allPassed = false;
            }
        } else {
            $tests[] = "✗ No sample product found";
            $allPassed = false;
        }
    } else {
        $tests[] = "✗ No products found in products table";
        $allPassed = false;
    }
    
    echo json_encode([
        'success' => $allPassed,
        'message' => $allPassed ? 'All tests passed!' : 'Some tests failed',
        'tests' => $tests
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Test failed: ' . $e->getMessage(),
        'tests' => $tests ?? []
    ]);
}
?>
