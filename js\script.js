// Real-time clock functionality
function updateClock() {
  const now = new Date();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  const ampm = hours >= 12 ? 'PM' : 'AM';
  const displayHours = hours % 12 || 12;
  const displayMinutes = minutes.toString().padStart(2, '0');
  
  const timeString = `${displayHours}:${displayMinutes} ${ampm}`;
  document.getElementById('clock').textContent = timeString;
}

// Update clock immediately and then every second
updateClock();
setInterval(updateClock, 1000);

// Sticky navbar functionality
function handleStickyNavbar() {
  const navbar = document.querySelector('.navbar');
  const scrollPosition = window.scrollY;
  
  if (scrollPosition > 100) {
    navbar.classList.add('sticky');
  } else {
    navbar.classList.remove('sticky');
  }
}

window.addEventListener('scroll', handleStickyNavbar);

// Back to top button functionality
function handleBackToTop() {
  const backToTopBtn = document.getElementById('backToTop');
  const scrollPosition = window.scrollY;
  
  if (scrollPosition > 300) {
    backToTopBtn.classList.add('visible');
  } else {
    backToTopBtn.classList.remove('visible');
  }
}

function scrollToTop() {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
}

window.addEventListener('scroll', handleBackToTop);

// Hero image auto-switching functionality
let currentHeroImage = 0;
const heroImages = document.querySelectorAll('.hero-image');
const heroImageCount = heroImages.length;

function switchHeroImage() {
  // Remove active class from current image
  heroImages[currentHeroImage].classList.remove('active');
  
  // Move to next image
  currentHeroImage = (currentHeroImage + 1) % heroImageCount;
  
  // Add active class to new image
  heroImages[currentHeroImage].classList.add('active');
}

// Switch hero image every 4 seconds
setInterval(switchHeroImage, 4000);

// Product carousel functionality
let currentProductIndex = 0;
let featuredProducts = [];
const productsContainer = document.getElementById('productsContainer');
const prevBtn = document.getElementById('prevBtn');
const nextBtn = document.getElementById('nextBtn');
const visibleProducts = 3; // Number of products visible at once

// Load featured products from database
async function loadFeaturedProducts() {
  try {
    // Show loading state
    if (productsContainer) {
      productsContainer.innerHTML = '<div class="loading">Loading products...</div>';
    
      // Fetch products from our PHP endpoint
      const response = await fetch('get_featured_products.php');
      
      if (!response.ok) {
        throw new Error(`Server responded with status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        featuredProducts = result.data;
        console.log('Loaded', featuredProducts.length, 'featured products');
        renderFeaturedProducts();
      } else {
        throw new Error(result.error || 'Failed to load products');
      }
    }
  } catch (error) {
    console.error('Error loading products:', error.message);
    
    // Fallback data if database connection fails
    featuredProducts = [
      {
        id: 1,
        name: "Japanese Matcha Kit Kat",
        description: "Green tea flavored chocolate wafer bars",
        image: "img/products/placeholder-1.png"
      },
      {
        id: 2,
        name: "Turkish Delight",
        description: "Rosewater flavored gel candy with pistachios",
        image: "img/products/placeholder-2.png"
      },
      {
        id: 3,
        name: "Belgian Chocolate Truffles",
        description: "Rich dark chocolate truffles with hazelnut",
        image: "img/products/placeholder-3.png"
      },
      {
        id: 4,
        name: "Indian Masala Chips",
        description: "Spicy potato chips with traditional spices",
        image: "img/products/placeholder-1.png"
      },
      {
        id: 5,
        name: "Brazilian Brigadeiros",
        description: "Chocolate fudge balls rolled in sprinkles",
        image: "img/products/placeholder-2.png"
      }
    ];
    
    renderFeaturedProducts();
  }
}

// Render featured products in the carousel
function renderFeaturedProducts() {
  if (!productsContainer) return;
  
  productsContainer.innerHTML = '';
  
  featuredProducts.forEach((product, index) => {
    const productCard = document.createElement('div');
    productCard.className = 'product-card';
    productCard.setAttribute('data-product-id', product.id);
    
    // Create image path - handle both database and fallback paths
    const imagePath = product.image.includes('placeholder') 
      ? product.image 
      : `img/products/${product.image}`;
    
    productCard.innerHTML = `
      <div class="product-image-container">
        <img class="product-image" alt="${product.name}" src="${imagePath}" 
             onerror="this.src='img/products/default-snack.png'" />
      </div>
      <h3 class="product-name">${product.name}</h3>
      <p class="product-description">${product.description}</p>
    `;
    
    // Add click event to navigate to product detail
    productCard.addEventListener('click', function() {
      window.location.href = `shop.html?product=${product.id}`;
    });
    
    productsContainer.appendChild(productCard);
  });
  
  // Reset carousel position
  currentProductIndex = 0;
  updateCarousel();
  
  // Add hover effects to product cards
  document.querySelectorAll('.product-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-8px)';
    });
    
    card.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
    });
  });
}

// Update carousel position with smooth transition
function updateCarousel() {
  if (!productsContainer) return;
  
  const productCards = document.querySelectorAll('.product-card');
  if (productCards.length === 0) return;
  
  // Get the width of a single card including margins
  const cardWidth = productCards[0].offsetWidth + 32; // card width + gap
  
  // Calculate the translation amount
  const translateX = -(currentProductIndex * cardWidth);
  
  // Apply smooth transition
  productsContainer.style.transition = 'transform 0.5s ease';
  productsContainer.style.transform = `translateX(${translateX}px)`;
}

// Navigation functions
function showNextProducts() {
  if (currentProductIndex < featuredProducts.length - visibleProducts) {
    currentProductIndex++;
    updateCarousel();
  } else {
    // Wrap around to the beginning with animation
    currentProductIndex = 0;
    updateCarousel();
  }
}

function showPrevProducts() {
  if (currentProductIndex > 0) {
    currentProductIndex--;
    updateCarousel();
  } else {
    // Wrap around to the end with animation
    currentProductIndex = Math.max(0, featuredProducts.length - visibleProducts);
    updateCarousel();
  }
}

// Event listeners for carousel buttons
if (prevBtn && nextBtn) {
  nextBtn.addEventListener('click', function(e) {
    e.preventDefault();
    showNextProducts();
  });
  
  prevBtn.addEventListener('click', function(e) {
    e.preventDefault();
    showPrevProducts();
  });
}

// Initialize products when page loads
document.addEventListener('DOMContentLoaded', function() {
  // Only load featured products if we're on the homepage
  if (document.getElementById('productsContainer')) {
    loadFeaturedProducts();
  }
});

// Quiz functionality
const quizQuestions = [
  {
    question: "What's your ideal snack time?",
    options: [
      { text: "Morning energy boost", type: "energetic" },
      { text: "Afternoon pick-me-up", type: "balanced" },
      { text: "Late night munchies", type: "indulgent" },
      { text: "Anytime I'm hungry!", type: "adventurous" }
    ]
  },
  {
    question: "Which flavor profile excites you most?",
    options: [
      { text: "Sweet and fruity", type: "sweet" },
      { text: "Salty and savory", type: "savory" },
      { text: "Spicy and bold", type: "spicy" },
      { text: "Mix of everything", type: "adventurous" }
    ]
  },
  {
    question: "Your ideal snack texture is:",
    options: [
      { text: "Crunchy and crispy", type: "crunchy" },
      { text: "Soft and chewy", type: "chewy" },
      { text: "Smooth and creamy", type: "smooth" },
      { text: "Variety is the spice of life", type: "adventurous" }
    ]
  },
  {
    question: "When trying new snacks, you:",
    options: [
      { text: "Stick to familiar favorites", type: "classic" },
      { text: "Try one new thing at a time", type: "cautious" },
      { text: "Love exploring exotic flavors", type: "adventurous" },
      { text: "Go for the weirdest option", type: "bold" }
    ]
  }
];

const quizResults = {
  adventurous: {
    title: "The Global Explorer 🌍",
    description: "You're always ready for a flavor adventure! You love trying exotic snacks from different cultures and aren't afraid of bold, unique tastes. Your snack drawer is like a passport to the world.",
    categories: ["International Snacks", "Exotic Flavors", "Fusion Treats", "World Cuisine"]
  },
  sweet: {
    title: "The Sweet Dreamer 🍭",
    description: "Life is sweeter with you around! You gravitate toward fruity, chocolatey, and dessert-like snacks. You believe every moment deserves a little sweetness.",
    categories: ["Desserts", "Chocolates", "Candies", "Sweet Pastries"]
  },
  savory: {
    title: "The Savory Connoisseur 🧀",
    description: "You appreciate the finer things in snacking! Chips, crackers, nuts, and cheese-based treats are your go-to. You know that savory snacks are the real MVPs.",
    categories: ["Chips & Crackers", "Nuts & Seeds", "Cheese Snacks", "Savory Bites"]
  },
  spicy: {
    title: "The Heat Seeker 🌶️",
    description: "You like your snacks with a kick! Spicy chips, hot nuts, and fiery treats are your specialty. You're not just eating snacks, you're having an experience.",
    categories: ["Spicy Snacks", "Hot Chips", "Chili Treats", "Fiery Flavors"]
  },
  classic: {
    title: "The Timeless Traditionalist 🥨",
    description: "You know what you like and you like what you know! Classic snacks like pretzels, cookies, and traditional treats never go out of style for you.",
    categories: ["Classic Cookies", "Traditional Snacks", "Comfort Foods", "Nostalgic Treats"]
  }
};

let currentQuizQuestion = 0;
let quizAnswers = [];

function openQuiz() {
  const modal = document.getElementById('quizModal');
  modal.classList.add('active');
  document.body.style.overflow = 'hidden';
  
  currentQuizQuestion = 0;
  quizAnswers = [];
  showQuizQuestion();
}

function closeQuiz() {
  const modal = document.getElementById('quizModal');
  modal.classList.remove('active');
  document.body.style.overflow = 'auto';
}

function showQuizQuestion() {
  const quizBody = document.getElementById('quizBody');
  
  if (currentQuizQuestion < quizQuestions.length) {
    const question = quizQuestions[currentQuizQuestion];
    
    quizBody.innerHTML = `
      <div class="quiz-question">
        <h3>Question ${currentQuizQuestion + 1} of ${quizQuestions.length}</h3>
        <h3>${question.question}</h3>
        <div class="quiz-options">
          ${question.options.map((option, index) => `
            <div class="quiz-option" onclick="selectQuizOption('${option.type}', ${index})">
              ${option.text}
            </div>
          `).join('')}
        </div>
      </div>
    `;
  } else {
    showQuizResult();
  }
}

function selectQuizOption(type, index) {
  // Visual feedback
  const options = document.querySelectorAll('.quiz-option');
  options.forEach(option => option.classList.remove('selected'));
  options[index].classList.add('selected');
  
  // Store answer
  quizAnswers.push(type);
  
  // Move to next question after a short delay
  setTimeout(() => {
    currentQuizQuestion++;
    showQuizQuestion();
  }, 500);
}

function showQuizResult() {
  // Calculate result based on most common answer type
  const answerCounts = {};
  quizAnswers.forEach(answer => {
    answerCounts[answer] = (answerCounts[answer] || 0) + 1;
  });
  
  const resultType = Object.keys(answerCounts).reduce((a, b) => 
    answerCounts[a] > answerCounts[b] ? a : b
  );
  
  const result = quizResults[resultType] || quizResults.adventurous;
  
  // Save quiz result to profile
  saveQuizResultToProfile(result);
  
  const quizBody = document.getElementById('quizBody');
  quizBody.innerHTML = `
    <div class="quiz-result">
      <h3>${result.title}</h3>
      <p>${result.description}</p>
      
      <div class="quiz-suggestions">
        <h4>🛍️ Recommended Categories for You:</h4>
        <div class="suggestion-categories">
          ${result.categories.map(category => `
            <a href="shop.html?category=${encodeURIComponent(category)}" class="category-tag">
              ${category}
            </a>
          `).join('')}
        </div>
      </div>
      
      <div class="quiz-buttons">
        <button class="quiz-btn" onclick="restartQuiz()">Take Again</button>
        <button class="quiz-btn" onclick="closeQuiz()">Explore Snacks</button>
      </div>
    </div>
  `;
}

function saveQuizResultToProfile(result) {
  // Save quiz result to localStorage for profile page
  const quizData = {
    result: result,
    timestamp: new Date().toISOString(),
    answers: quizAnswers
  };
  
  localStorage.setItem('quizResult', JSON.stringify(quizData));
  console.log('Quiz result saved to profile:', result.title);
}

function restartQuiz() {
  currentQuizQuestion = 0;
  quizAnswers = [];
  showQuizQuestion();
}

// Newsletter functionality
function openNewsletter() {
  const modal = document.getElementById('newsletterModal');
  modal.classList.add('active');
  document.body.style.overflow = 'hidden';
}

function closeNewsletter() {
  const modal = document.getElementById('newsletterModal');
  modal.classList.remove('active');
  document.body.style.overflow = 'auto';
}

// Logout functionality
function logout() {
  console.log("Logout function called");
  // Show confirmation dialog
  const confirmLogout = confirm('Are you sure you want to logout?');
  
  if (confirmLogout) {
    console.log("Logout confirmed");
    // Add logout animation
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    
    setTimeout(() => {
      // Clear user data from localStorage
      localStorage.removeItem('quizResult');
      localStorage.removeItem('shoppingCart');
      localStorage.removeItem('currentSnackAtlasUser');
      localStorage.removeItem('cart');
      localStorage.removeItem('savedProducts');
      localStorage.removeItem('profileData');
      
      console.log('User data cleared, redirecting to login page');
      
      // Redirect to login page
      window.location.href = 'login.html';
    }, 500);
  } else {
    console.log("Logout cancelled");
    document.body.style.opacity = '1';
  }
}

// Contact form functionality
function handleContactForm() {
  const form = document.getElementById('contactForm');
  
  form.addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(form);
    const data = {
      fullName: formData.get('fullName'),
      email: formData.get('email'),
      message: formData.get('message'),
      to: '<EMAIL>'
    };
    
    // Show loading state
    const submitBtn = form.querySelector('.submit-button');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Sending...';
    submitBtn.disabled = true;
    
    try {
      // Simulate sending email (replace with actual email service)
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Show success message below the button
      showContactSuccessMessage();
      form.reset();
      
    } catch (error) {
      console.error('Error sending message:', error);
      showContactErrorMessage();
    } finally {
      submitBtn.textContent = originalText;
      submitBtn.disabled = false;
    }
  });
}

// Show contact form success message
function showContactSuccessMessage() {
  const successMessage = document.getElementById('contactSuccessMessage');
  successMessage.style.display = 'block';
  
  // Hide after 10 seconds
  setTimeout(() => {
    successMessage.style.display = 'none';
  }, 10000);
}

// Show contact form error message
function showContactErrorMessage() {
  const successMessage = document.getElementById('contactSuccessMessage');
  successMessage.innerHTML = '❌ Failed to send message. Please try again.';
  successMessage.style.background = 'rgba(220, 53, 69, 0.9)';
  successMessage.style.display = 'block';
  
  // Hide after 10 seconds
  setTimeout(() => {
    successMessage.style.display = 'none';
    successMessage.innerHTML = '✅ Message sent successfully! We\'ll get back to you soon.';
    successMessage.style.background = 'rgba(40, 167, 69, 0.9)';
  }, 10000);
}

// Newsletter form functionality
function handleNewsletterForm() {
  const form = document.getElementById('newsletterForm');
  
  form.addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const email = form.querySelector('input[type="email"]').value;
    
    // Show loading state
    const submitBtn = form.querySelector('button');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Subscribing...';
    submitBtn.disabled = true;
    
    try {
      // Simulate newsletter subscription
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      showNotification('Successfully subscribed to our newsletter!', 'success');
      closeNewsletter();
      form.reset();
      
    } catch (error) {
      console.error('Error subscribing:', error);
      showNotification('Failed to subscribe. Please try again.', 'error');
    } finally {
      submitBtn.textContent = originalText;
      submitBtn.disabled = false;
    }
  });
}

// Notification system
function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.textContent = message;
  
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    z-index: 3000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    animation: slideInRight 0.3s ease-out;
  `;
  
  document.body.appendChild(notification);
  
  setTimeout(() => {
    notification.style.animation = 'slideOutRight 0.3s ease-out';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 4000);
}

// Add notification animations
const style = document.createElement('style');
style.textContent = `
  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(100px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes slideOutRight {
    from {
      opacity: 1;
      transform: translateX(0);
    }
    to {
      opacity: 0;
      transform: translateX(100px);
    }
  }
`;
document.head.appendChild(style);

// Smooth scrolling for navigation links
document.querySelectorAll('.nav-link').forEach(link => {
  link.addEventListener('click', function(e) {
    const href = this.getAttribute('href');
    
    // Skip if it's an external link or dropdown
    if (!href.startsWith('#') || this.classList.contains('dropdown-toggle')) {
      return;
    }
    
    e.preventDefault();
    
    // Remove active class from all links
    document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
    
    // Add active class to clicked link
    this.classList.add('active');
    
    // Get target section
    const targetId = href.substring(1);
    let targetSection;
    
    switch(targetId) {
      case 'home':
        targetSection = document.querySelector('.hero-section');
        break;
      case 'about':
        targetSection = document.querySelector('.about-section');
        break;
      case 'shop':
        targetSection = document.querySelector('.shop-section');
        break;
      case 'contact':
        targetSection = document.querySelector('.contact-section');
        break;
      default:
        targetSection = document.querySelector('.hero-section');
    }
    
    if (targetSection) {
      targetSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  });
});

// Handle dropdown navigation
document.querySelectorAll('.dropdown-item').forEach(item => {
  item.addEventListener('click', function(e) {
    const href = this.getAttribute('href');
    
    if (href.startsWith('#')) {
      e.preventDefault();
      
      const targetId = href.substring(1);
      const targetSection = document.querySelector(`#${targetId}`);
      
      if (targetSection) {
        targetSection.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    }
  });
});

// Close modals when clicking outside
document.addEventListener('click', function(e) {
  const quizModal = document.getElementById('quizModal');
  const newsletterModal = document.getElementById('newsletterModal');
  
  if (e.target === quizModal) {
    closeQuiz();
  }
  
  if (e.target === newsletterModal) {
    closeNewsletter();
  }
});

// Handle escape key to close modals
document.addEventListener('keydown', function(e) {
  if (e.key === 'Escape') {
    closeQuiz();
    closeNewsletter();
  }
});

// Enhanced hover effects for buttons
document.querySelectorAll('button').forEach(button => {
  button.addEventListener('mouseenter', function() {
    if (!this.disabled) {
      this.style.transform = 'scale(1.05)';
    }
  });
  
  button.addEventListener('mouseleave', function() {
    this.style.transform = 'scale(1)';
  });
});

// Add click effects to nav icons
document.querySelectorAll('.nav-icon').forEach(icon => {
  icon.addEventListener('click', function() {
    this.style.transform = 'scale(0.95)';
    setTimeout(() => {
      this.style.transform = 'scale(1)';
    }, 150);
  });
});

// Fade-in animations for sections
function initializeFadeInAnimations() {
  const sections = document.querySelectorAll('section');
  
  sections.forEach(section => {
    section.classList.add('fade-in-section');
  });
  
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
      }
    });
  }, observerOptions);
  
  sections.forEach(section => {
    observer.observe(section);
  });
  
  // Make the hero section immediately visible
  document.querySelector('.hero-section').classList.add('visible');
}

// Auto-refresh products every 45 seconds to show different random products
setInterval(async () => {
  await loadProducts();
  currentProductIndex = 0; // Reset to beginning
}, 45000);

// Setup logout button
function setupLogoutButton() {
  const logoutButton = document.getElementById('logoutButton');
  if (logoutButton) {
    logoutButton.addEventListener('click', function(e) {
      e.preventDefault();
      logout();
    });
    console.log('Logout button initialized');
  } else {
    console.error('Logout button not found in the DOM');
  }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
  // Only load featured products if we're on the homepage
  if (document.getElementById('productsContainer')) {
    loadFeaturedProducts();
  }
  
  // Setup logout button
  setupLogoutButton();
});
