<?php
include 'db_connection.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $id = $_POST['id'];
    $name = $_POST['name'];
    $description = $_POST['description'];
    $price = $_POST['price'];
    $ingredients = $_POST['ingredients'];
    $nutrition = $_POST['nutrition'];
    $category = $_POST['category'];
    $stock_quantity = $_POST['stock_quantity'];

    $image = $_FILES['image']['name'];
    $uploadDir = "uploads/";

    // Auto-create uploads folder if missing
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    $target = $uploadDir . basename($image);

    if (move_uploaded_file($_FILES['image']['tmp_name'], $target)) {
        $sql = "INSERT INTO products 
                (id, name, description, price, image, ingredients, nutrition, category, stock_quantity)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssssssssi", $id, $name, $description, $price, $image, $ingredients, $nutrition, $category, $stock_quantity);
        
        if ($stmt->execute()) {
            echo "<p style='color:green;'>✅ Product added successfully.</p>";
            echo "<a href='admin.php'><button>Back to Admin</button></a>";
        } else {
            echo "<p style='color:red;'>❌ Database error: " . $stmt->error . "</p>";
        }
        $stmt->close();
    } else {
        echo "<p style='color:red;'>❌ Failed to upload image.</p>";
        echo "<p>Check that your <code>uploads/</code> folder exists and is writable.</p>";
    }

    $conn->close();
}
?>
