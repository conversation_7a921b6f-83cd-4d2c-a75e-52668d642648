// Real-time  clock functionality
function updateClock() {
  const now = new Date();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  const ampm = hours >= 12 ? 'PM' : 'AM';
  const displayHours = hours % 12 || 12;
  const displayMinutes = minutes.toString().padStart(2, '0');
  
  const timeString = `${displayHours}:${displayMinutes} ${ampm}`;
  document.getElementById('clock').textContent = timeString;
}

// Update clock immediately and then every second
updateClock();
setInterval(updateClock, 1000);

// Cart data structure
let cartItems = [];

const SHIPPING_FEE = 5.00;

// DOM elements (will be initialized after DOM loads)
let cartItemsContainer;
let emptyCartContainer;
let cartSummaryContainer;
let realTimeClock;
let navCenter;

// Load cart items from database
async function loadCartItems() {
  try {
    console.log('Fetching cart items...');
    let response;
    let data;
    
    try {
      // Try the main endpoint first
      response = await fetch('api/get-cart.php');
      if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
      
      const text = await response.text();
      data = JSON.parse(text);
    } catch (primaryError) {
      console.warn('Primary endpoint failed, trying fallback:', primaryError);
      
      // Try the fallback endpoint
      response = await fetch('api/get-cart-simple.php');
      if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
      
      const text = await response.text();
      data = JSON.parse(text);
    }
    
    if (data.success) {
      console.log('Cart data received:', data);
      cartItems = data.cart || [];
      console.log('Processed cart items:', cartItems);

      // Debug: Log each item structure
      cartItems.forEach((item, index) => {
        console.log(`Item ${index}:`, {
          id: item.id,
          name: item.name,
          price: item.price,
          image: item.image,
          quantity: item.quantity
        });
      });

      updateCartDisplay();
      updateCartSummary();
    } else {
      console.error('API error:', data.message);
      showNotification('Failed to load cart items: ' + data.message, 'error');
    }
  } catch (error) {
    console.error('Error loading cart:', error);
    showNotification('Error loading cart items: ' + error.message, 'error');
    
    // Show empty cart as fallback
    cartItems = [];
    updateCartDisplay();
    updateCartSummary();
  }
}

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing cart...');

    // Initialize DOM elements
    cartItemsContainer = document.getElementById('cartItems');
    emptyCartContainer = document.getElementById('emptyCart');
    cartSummaryContainer = document.getElementById('cartSummary');
    realTimeClock = document.getElementById('clock');
    navCenter = document.getElementById('navCenter');

    // Check if elements exist
    if (!cartItemsContainer) console.error('cartItems element not found');
    if (!emptyCartContainer) console.error('emptyCart element not found');
    if (!cartSummaryContainer) console.error('cartSummary element not found');
    if (!navCenter) console.error('navCenter element not found');

    loadCartItems(); // Replace updateCartDisplay() with loadCartItems()
    startRealTimeClock();

    // Add smooth scrolling
    document.documentElement.style.scrollBehavior = 'smooth';
});

// Real-time clock function
function startRealTimeClock() {
    function updateClock() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour12: true,
            hour: '2-digit',
            minute: '2-digit'
        });
       
        realTimeClock.textContent = ` ${timeString}`;
    }
    
    updateClock(); // Initial call
    setInterval(updateClock, 1000); // Update every second
}

// Update quantity function
async function updateQuantity(itemId, change) {
    console.log('Updating quantity for item:', itemId, 'change:', change);

    // Convert itemId to string for comparison since it might come as string from HTML
    const itemIdStr = String(itemId);
    const item = cartItems.find(item => String(item.id) === itemIdStr);

    if (!item) {
        console.error('Item not found:', itemId, 'Available items:', cartItems.map(i => i.id));
        return;
    }

    const newQuantity = item.quantity + change;

    // Minimum quantity is 1
    if (newQuantity < 1) return;
    
    try {
        // Update in database
        const response = await fetch('api/update-cart-quantity.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                product_id: itemId,
                quantity: newQuantity
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Update local item
            item.quantity = newQuantity;
            
            // Update display
            const quantityDisplay = document.getElementById(`quantity-${itemId}`);
            const totalDisplay = document.getElementById(`total-${itemId}`);
            const decreaseBtn = quantityDisplay.parentElement.querySelector('.decrease');
            
            quantityDisplay.textContent = item.quantity;
            totalDisplay.textContent = (parseFloat(item.price) * item.quantity).toFixed(2);
            
            // Disable decrease button if quantity is 1
            decreaseBtn.disabled = item.quantity <= 1;
            
            // Update cart summary
            updateCartSummary();
            
            // Add visual feedback
            quantityDisplay.style.transform = 'scale(1.2)';
            quantityDisplay.style.color = '#8b4513';
            setTimeout(() => {
                quantityDisplay.style.transform = 'scale(1)';
                quantityDisplay.style.color = '#333';
            }, 200);
        } else {
            showNotification('Failed to update quantity', 'error');
        }
    } catch (error) {
        console.error('Error updating quantity:', error);
        showNotification('Error updating quantity', 'error');
    }
}

// Remove item function
async function removeItem(itemId) {
    console.log('Removing item:', itemId);

    // Convert itemId to string for comparison
    const itemIdStr = String(itemId);
    const itemElement = document.querySelector(`[data-item-id="${itemIdStr}"]`);

    if (!itemElement) {
        console.error('Item element not found:', itemIdStr);
        return;
    }

    // Add removal animation
    itemElement.classList.add('removing');
    
    try {
        // Remove from database
        const response = await fetch('api/remove-from-cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ product_id: itemId })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Remove from cart data after animation
            setTimeout(() => {
                const itemIdStr = String(itemId);
                cartItems = cartItems.filter(item => String(item.id) !== itemIdStr);
                updateCartDisplay();
                updateCartSummary();
                showNotification('Item removed from cart', 'success');
            }, 300);
        } else {
            showNotification('Failed to remove item', 'error');
        }
    } catch (error) {
        console.error('Error removing item:', error);
        showNotification('Error removing item', 'error');
    }
}

// Update cart display
function updateCartDisplay() {
    const cartContent = document.querySelector('.cart-content');

    if (cartItems.length === 0) {
        cartItemsContainer.style.display = 'none';
        emptyCartContainer.style.display = 'block';
        cartSummaryContainer.style.display = 'none';

        // Hide nav center when cart is empty (only show "Continue Shopping" button)
        if (navCenter) {
            navCenter.style.display = 'none';
        }

        // Add empty-state class to center the empty cart
        if (cartContent) {
            cartContent.classList.add('empty-state');
        }
        return;
    }

    cartItemsContainer.style.display = 'block';
    emptyCartContainer.style.display = 'none';
    cartSummaryContainer.style.display = 'block';

    // Show nav center when cart has items
    if (navCenter) {
        navCenter.style.display = 'block';
    }

    // Remove empty-state class when cart has items
    if (cartContent) {
        cartContent.classList.remove('empty-state');
    }
    
    // Clear and rebuild cart items
    cartItemsContainer.innerHTML = '';
    
    cartItems.forEach(item => {
        const cartItemElement = createCartItemElement(item);
        cartItemsContainer.appendChild(cartItemElement);
    });
}

// Create cart item element
function createCartItemElement(item) {
    const itemElement = document.createElement('div');
    itemElement.className = 'cart-item';
    itemElement.setAttribute('data-item-id', item.id);

    // Ensure price is a number
    const price = parseFloat(item.price || 0);

    // Handle image path properly
    let imagePath;
    if (item.image && item.image.startsWith('http')) {
        imagePath = item.image;
    } else if (item.image) {
        imagePath = `img/products/${item.image}`;
    } else {
        imagePath = 'img/products/default-snack.png';
    }

    itemElement.innerHTML = `
        <div class="item-image">
            <img src="${imagePath}" alt="${item.name}" onerror="this.src='img/products/default-snack.png'">
        </div>
        <div class="item-details">
            <h3 class="item-name">${item.name || 'Product Name'}</h3>
            <p class="item-description">${item.description || 'No description available'}</p>
            <div class="item-price">RM <span class="price-value">${price.toFixed(2)}</span></div>
        </div>
        <div class="quantity-controls">
            <button class="quantity-btn decrease" onclick="updateQuantity('${item.id}', -1)" ${item.quantity <= 1 ? 'disabled' : ''}>−</button>
            <span class="quantity-display" id="quantity-${item.id}">${item.quantity}</span>
            <button class="quantity-btn increase" onclick="updateQuantity('${item.id}', 1)">+</button>
        </div>
        <div class="item-total">
            RM <span class="total-value" id="total-${item.id}">${(price * item.quantity).toFixed(2)}</span>
        </div>
        <button class="remove-btn" onclick="removeItem('${item.id}')">
            <span>×</span>
        </button>
    `;

    return itemElement;
}

// Update cart summary
function updateCartSummary() {
    const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);
    const subtotal = cartItems.reduce((sum, item) => sum + (parseFloat(item.price) * item.quantity), 0);
    const grandTotal = subtotal + SHIPPING_FEE;
    
    // Update summary display
    document.getElementById('itemCount').textContent = totalItems;
    document.getElementById('subtotalAmount').textContent = subtotal.toFixed(2);
    document.getElementById('shippingFee').textContent = SHIPPING_FEE.toFixed(2);
    document.getElementById('grandTotal').textContent = grandTotal.toFixed(2);
    
    // Disable checkout button if cart is empty
    const checkoutBtn = document.querySelector('.checkout-btn');
    checkoutBtn.disabled = cartItems.length === 0;
}

// Navigation functions
function backToShop() {
    // Redirect to shop page
    window.location.href = 'shop.html';
}

function proceedToCheckout() {
    if (cartItems.length === 0) {
        alert('Your cart is empty. Please add some items before checkout.');
        return;
    }
    
    // Store cart data in localStorage for checkout page
    localStorage.setItem('cartData', JSON.stringify({
        items: cartItems,
        subtotal: cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0),
        shipping: SHIPPING_FEE,
        total: cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0) + SHIPPING_FEE
    }));
    
    // Navigate to checkout page
    window.location.href = 'checkout.html';
}

// Add visual feedback for interactions
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('quantity-btn') || e.target.classList.contains('remove-btn')) {
        // Add ripple effect
        const button = e.target;
        const ripple = document.createElement('span');
        ripple.className = 'ripple';
        button.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 300);
    }
});

// Add hover effects for cart items
document.addEventListener('mouseover', function(e) {
    if (e.target.closest('.cart-item')) {
        const cartItem = e.target.closest('.cart-item');
        cartItem.style.transform = 'translateY(-2px)';
        cartItem.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.1)';
    }
});

document.addEventListener('mouseout', function(e) {
    if (e.target.closest('.cart-item')) {
        const cartItem = e.target.closest('.cart-item');
        cartItem.style.transform = 'translateY(0)';
        cartItem.style.boxShadow = 'none';
    }
});

// Keyboard navigation support
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        // Clear any active states or close modals if needed
    }
});

// Auto-save cart data to localStorage
function saveCartToStorage() {
    localStorage.setItem('shoppingCart', JSON.stringify(cartItems));
}

// Load cart data from localStorage
function loadCartFromStorage() {
    const savedCart = localStorage.getItem('shoppingCart');
    if (savedCart) {
        cartItems = JSON.parse(savedCart);
        updateCartDisplay();
        updateCartSummary();
    }
}

// Save cart whenever it changes
function updateCartAndSave() {
    updateCartSummary();
    saveCartToStorage();
}

// Add notification system for user feedback
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        z-index: 1000;
        animation: slideInRight 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Enhanced remove item with confirmation
function removeItemWithConfirmation(itemId) {
    const item = cartItems.find(item => item.id === itemId);
    if (!item) return;
    
    if (confirm(`Are you sure you want to remove "${item.name}" from your cart?`)) {
        removeItem(itemId);
        showNotification(`${item.name} removed from cart`, 'success');
    }
}

// Real-time clock functionality
function updateClock() {
    const now = new Date();
    const hours = now.getHours();
    const minutes = now.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours % 12 || 12;
    const displayMinutes = minutes.toString().padStart(2, '0');
    
    const timeString = `${displayHours}:${displayMinutes} ${ampm}`;
    document.getElementById('clock').textContent = timeString;
}

// Load cart from storage on page load
document.addEventListener('DOMContentLoaded', function() {
    loadCartFromStorage();
});

console.log('Shopping cart initialized successfully!');

document.addEventListener('DOMContentLoaded', loadCartItems);

document.addEventListener('DOMContentLoaded', async () => {
    try {
        const res = await fetch('../get-cart.php'); // adjust path if needed
        const data = await res.json();

        if (data.success && data.cart.length > 0) {
            const cartItemsContainer = document.getElementById('cartItems');
            cartItemsContainer.innerHTML = ''; // Clear placeholder

            data.cart.forEach((item, index) => {
                const itemHTML = `
                <div class="cart-item" data-id="${item.id}">
                    <div class="item-image">
                        <img src="${item.image}" alt="${item.name}" />
                    </div>
                    <div class="item-details">
                        <h3 class="item-name">${item.name}</h3>
                        <p class="item-description">${item.description}</p>
                        <div class="item-price">RM ${parseFloat(item.price).toFixed(2)}</div>
                    </div>
                </div>
                `;
                cartItemsContainer.insertAdjacentHTML('beforeend', itemHTML);
            });
        } else {
            document.getElementById('cartItems').innerHTML = `<p>Your cart is looking... tragically empty 🥲</p>`;
        }
    } catch (err) {
        console.error('Failed to load cart:', err);
    }
});
