-- Create wishlist table to store user's saved products
CREATE TABLE `wishlist` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `product_id` varchar(20) NOT NULL,
  `added_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  <PERSON>IQUE KEY `unique_user_product` (`user_id`, `product_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_product_id` (`product_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Sample queries for wishlist operations:

-- Add item to wishlist
/*
INSERT INTO wishlist (user_id, product_id) 
VALUES (1, 'PRODUCT_ID_HERE')
ON DUPLICATE KEY UPDATE added_at = CURRENT_TIMESTAMP;
*/

-- Remove item from wishlist
/*
DELETE FROM wishlist 
WHERE user_id = 1 AND product_id = 'PRODUCT_ID_HERE';
*/

-- Get user's wishlist with product details
/*
SELECT 
    w.id as wishlist_id,
    w.user_id,
    w.product_id,
    w.added_at,
    p.name as product_name,
    p.description,
    p.price,
    p.image,
    p.category
FROM wishlist w
JOIN products p ON w.product_id = p.id
WHERE w.user_id = 1
ORDER BY w.added_at DESC;
*/

-- Check if product is in user's wishlist
/*
SELECT COUNT(*) as is_liked
FROM wishlist 
WHERE user_id = 1 AND product_id = 'PRODUCT_ID_HERE';
*/

-- Get wishlist count for user
/*
SELECT COUNT(*) as wishlist_count
FROM wishlist 
WHERE user_id = 1;
*/
