<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

$host = 'localhost';
$dbname = 'group4_db';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Simple query to get cart items with stored product details
    $query = "SELECT * FROM cart ORDER BY added_at DESC";
    $stmt = $pdo->query($query);
    $cartItems = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Format the data to match expected structure
    $formattedItems = [];
    foreach ($cartItems as $item) {
        $formattedItems[] = [
            'id' => $item['product_id'],
            'name' => $item['product_name'],
            'description' => 'Product description', // Placeholder - could be fetched from products table if needed
            'price' => (float)($item['product_price'] ?? 0.00), // Use stored price
            'image' => $item['product_image'] ?? 'img/products/default-snack.png', // Use stored image or default
            'quantity' => (int)$item['quantity'] // Ensure it's an integer
        ];
    }

    echo json_encode([
        'success' => true,
        'cart' => $formattedItems
    ]);
} catch (PDOException $e) {
    // Log the error for debugging
    error_log("Database error in get-cart-simple.php: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
