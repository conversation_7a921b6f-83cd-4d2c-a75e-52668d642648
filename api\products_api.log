2025-06-29 19:19:34 - Request: /snackatlas/api/products.php?t=1751224774615
2025-06-29 19:19:34 - Parameters: page=1, search=, filter=
2025-06-29 19:19:34 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:19:34 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:19:34 - Success: Found 9 products, total: 13
2025-06-29 19:19:38 - Request: /snackatlas/api/products.php?page=1&search=s
2025-06-29 19:19:38 - Parameters: page=1, search=s, filter=
2025-06-29 19:19:38 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1 AND (name LIKE ? OR description LIKE ?)
2025-06-29 19:19:38 - Main Query: SELECT * FROM products WHERE 1=1 AND (name LIKE ? OR description LIKE ?) ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:19:38 - Success: Found 9 products, total: 9
2025-06-29 19:19:41 - Request: /snackatlas/api/products.php?page=1&search=sushi
2025-06-29 19:19:41 - Parameters: page=1, search=sushi, filter=
2025-06-29 19:19:41 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1 AND (name LIKE ? OR description LIKE ?)
2025-06-29 19:19:41 - Main Query: SELECT * FROM products WHERE 1=1 AND (name LIKE ? OR description LIKE ?) ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:19:41 - Success: Found 1 products, total: 1
2025-06-29 19:19:44 - Request: /snackatlas/api/products.php?page=1
2025-06-29 19:19:44 - Parameters: page=1, search=, filter=
2025-06-29 19:19:44 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:19:44 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:19:44 - Success: Found 9 products, total: 13
2025-06-29 19:19:46 - Request: /snackatlas/api/products.php?page=1&filter=name_asc
2025-06-29 19:19:46 - Parameters: page=1, search=, filter=name_asc
2025-06-29 19:19:46 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:19:46 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY name ASC LIMIT ? OFFSET ?
2025-06-29 19:19:46 - Success: Found 9 products, total: 13
2025-06-29 19:19:53 - Request: /snackatlas/api/products.php?page=1&filter=price_asc
2025-06-29 19:19:53 - Parameters: page=1, search=, filter=price_asc
2025-06-29 19:19:53 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:19:53 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY price ASC LIMIT ? OFFSET ?
2025-06-29 19:19:53 - Success: Found 9 products, total: 13
2025-06-29 19:19:56 - Request: /snackatlas/api/products.php?page=1&filter=price_desc
2025-06-29 19:19:56 - Parameters: page=1, search=, filter=price_desc
2025-06-29 19:19:56 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:19:56 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY price DESC LIMIT ? OFFSET ?
2025-06-29 19:19:56 - Success: Found 9 products, total: 13
2025-06-29 19:19:59 - Request: /snackatlas/api/products.php?page=1&filter=snacks
2025-06-29 19:19:59 - Parameters: page=1, search=, filter=snacks
2025-06-29 19:19:59 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1 AND category = ?
2025-06-29 19:19:59 - Main Query: SELECT * FROM products WHERE 1=1 AND category = ? LIMIT ? OFFSET ?
2025-06-29 19:19:59 - Success: Found 6 products, total: 6
2025-06-29 19:20:06 - Request: /snackatlas/api/products.php?page=1&filter=beverages
2025-06-29 19:20:06 - Parameters: page=1, search=, filter=beverages
2025-06-29 19:20:06 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1 AND category = ?
2025-06-29 19:20:06 - Main Query: SELECT * FROM products WHERE 1=1 AND category = ? LIMIT ? OFFSET ?
2025-06-29 19:20:06 - Success: Found 0 products, total: 0
2025-06-29 19:20:10 - Request: /snackatlas/api/products.php?page=1&filter=desserts
2025-06-29 19:20:10 - Parameters: page=1, search=, filter=desserts
2025-06-29 19:20:10 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1 AND category = ?
2025-06-29 19:20:10 - Main Query: SELECT * FROM products WHERE 1=1 AND category = ? LIMIT ? OFFSET ?
2025-06-29 19:20:10 - Success: Found 0 products, total: 0
2025-06-29 19:20:13 - Request: /snackatlas/api/products.php?page=1&filter=meals
2025-06-29 19:20:13 - Parameters: page=1, search=, filter=meals
2025-06-29 19:20:13 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1 AND category = ?
2025-06-29 19:20:13 - Main Query: SELECT * FROM products WHERE 1=1 AND category = ? LIMIT ? OFFSET ?
2025-06-29 19:20:13 - Success: Found 0 products, total: 0
2025-06-29 19:20:20 - Request: /snackatlas/api/products.php?page=1&search=csndk&filter=meals
2025-06-29 19:20:20 - Parameters: page=1, search=csndk, filter=meals
2025-06-29 19:20:20 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1 AND (name LIKE ? OR description LIKE ?) AND category = ?
2025-06-29 19:20:20 - Main Query: SELECT * FROM products WHERE 1=1 AND (name LIKE ? OR description LIKE ?) AND category = ? LIMIT ? OFFSET ?
2025-06-29 19:20:20 - Success: Found 0 products, total: 0
2025-06-29 19:20:22 - Request: /snackatlas/api/products.php?page=1&search=csndk
2025-06-29 19:20:22 - Parameters: page=1, search=csndk, filter=
2025-06-29 19:20:22 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1 AND (name LIKE ? OR description LIKE ?)
2025-06-29 19:20:22 - Main Query: SELECT * FROM products WHERE 1=1 AND (name LIKE ? OR description LIKE ?) ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:20:22 - Success: Found 0 products, total: 0
2025-06-29 19:20:26 - Request: /snackatlas/api/products.php?page=1
2025-06-29 19:20:26 - Parameters: page=1, search=, filter=
2025-06-29 19:20:26 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:20:26 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:20:26 - Success: Found 9 products, total: 13
2025-06-29 19:20:28 - Request: /snackatlas/api/products.php?page=1&search=vdnsvj
2025-06-29 19:20:28 - Parameters: page=1, search=vdnsvj, filter=
2025-06-29 19:20:28 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1 AND (name LIKE ? OR description LIKE ?)
2025-06-29 19:20:28 - Main Query: SELECT * FROM products WHERE 1=1 AND (name LIKE ? OR description LIKE ?) ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:20:28 - Success: Found 0 products, total: 0
2025-06-29 19:20:33 - Request: /snackatlas/api/products.php?page=1
2025-06-29 19:20:33 - Parameters: page=1, search=, filter=
2025-06-29 19:20:33 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:20:33 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:20:33 - Success: Found 9 products, total: 13
2025-06-29 19:21:17 - Request: /snackatlas/api/products.php?t=1751224877714
2025-06-29 19:21:17 - Parameters: page=1, search=, filter=
2025-06-29 19:21:17 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:21:17 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:21:17 - Success: Found 9 products, total: 13
2025-06-29 19:21:22 - Request: /snackatlas/api/products.php?page=1&filter=name_desc
2025-06-29 19:21:22 - Parameters: page=1, search=, filter=name_desc
2025-06-29 19:21:22 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:21:22 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY name DESC LIMIT ? OFFSET ?
2025-06-29 19:21:22 - Success: Found 9 products, total: 13
2025-06-29 19:21:25 - Request: /snackatlas/api/products.php?page=1
2025-06-29 19:21:25 - Parameters: page=1, search=, filter=
2025-06-29 19:21:25 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:21:25 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:21:25 - Success: Found 9 products, total: 13
2025-06-29 19:21:29 - Request: /snackatlas/api/products.php?t=1751224889328
2025-06-29 19:21:29 - Parameters: page=1, search=, filter=
2025-06-29 19:21:29 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:21:29 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:21:29 - Success: Found 9 products, total: 13
2025-06-29 19:21:38 - Request: /snackatlas/api/products.php?t=1751224898865
2025-06-29 19:21:38 - Parameters: page=1, search=, filter=
2025-06-29 19:21:38 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:21:38 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:21:38 - Success: Found 9 products, total: 13
2025-06-29 19:21:59 - Request: /snackatlas/api/products.php?t=1751224919953
2025-06-29 19:21:59 - Parameters: page=1, search=, filter=
2025-06-29 19:21:59 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:21:59 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:21:59 - Success: Found 9 products, total: 13
2025-06-29 19:22:06 - Request: /snackatlas/api/products.php?t=1751224926684
2025-06-29 19:22:06 - Parameters: page=1, search=, filter=
2025-06-29 19:22:06 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:22:06 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:22:06 - Success: Found 9 products, total: 13
2025-06-29 19:22:22 - Request: /snackatlas/api/products.php?t=1751224942905
2025-06-29 19:22:22 - Parameters: page=1, search=, filter=
2025-06-29 19:22:22 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:22:22 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:22:22 - Success: Found 9 products, total: 13
2025-06-29 19:22:27 - Request: /snackatlas/api/products.php?t=1751224947676
2025-06-29 19:22:27 - Parameters: page=1, search=, filter=
2025-06-29 19:22:27 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:22:27 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:22:27 - Success: Found 9 products, total: 13
2025-06-29 19:25:39 - Request: /snackatlas/api/products.php?t=1751225139587
2025-06-29 19:25:39 - Parameters: page=1, search=, filter=
2025-06-29 19:25:39 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:25:39 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:25:39 - Success: Found 9 products, total: 13
2025-06-29 19:25:51 - Request: /snackatlas/api/products.php?t=1751225151046
2025-06-29 19:25:51 - Parameters: page=1, search=, filter=
2025-06-29 19:25:51 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:25:51 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:25:51 - Success: Found 9 products, total: 13
2025-06-29 19:26:10 - Request: /snackatlas/api/products.php?t=1751225170202
2025-06-29 19:26:10 - Parameters: page=1, search=, filter=
2025-06-29 19:26:10 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:26:10 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:26:10 - Success: Found 9 products, total: 13
2025-06-29 19:26:17 - Request: /snackatlas/api/products.php?t=1751225177686
2025-06-29 19:26:17 - Parameters: page=1, search=, filter=
2025-06-29 19:26:17 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:26:17 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:26:17 - Success: Found 9 products, total: 13
2025-06-29 19:27:57 - Request: /snackatlas/api/products.php?t=1751225277373
2025-06-29 19:27:57 - Parameters: page=1, search=, filter=
2025-06-29 19:27:57 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:27:57 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:27:57 - Success: Found 9 products, total: 13
2025-06-29 19:28:04 - Request: /snackatlas/api/products.php?t=1751225284552
2025-06-29 19:28:04 - Parameters: page=1, search=, filter=
2025-06-29 19:28:04 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:28:04 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:28:04 - Success: Found 9 products, total: 13
2025-06-29 19:29:01 - Request: /snackatlas/api/products.php?t=1751225341593
2025-06-29 19:29:01 - Parameters: page=1, search=, filter=
2025-06-29 19:29:01 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:29:01 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:29:01 - Success: Found 9 products, total: 13
2025-06-29 19:29:06 - Request: /snackatlas/api/products.php?t=1751225346004
2025-06-29 19:29:06 - Parameters: page=1, search=, filter=
2025-06-29 19:29:06 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:29:06 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:29:06 - Success: Found 9 products, total: 13
2025-06-29 19:30:26 - Request: /snackatlas/api/products.php?t=1751225426126
2025-06-29 19:30:26 - Parameters: page=1, search=, filter=
2025-06-29 19:30:26 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:30:26 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:30:26 - Success: Found 9 products, total: 13
2025-06-29 19:30:34 - Request: /snackatlas/api/products.php?t=1751225434131
2025-06-29 19:30:34 - Parameters: page=1, search=, filter=
2025-06-29 19:30:34 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:30:34 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:30:34 - Success: Found 9 products, total: 13
2025-06-29 19:32:09 - Request: /snackatlas/api/products.php?t=1751225529072
2025-06-29 19:32:09 - Parameters: page=1, search=, filter=
2025-06-29 19:32:09 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:32:09 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:32:09 - Success: Found 9 products, total: 13
2025-06-29 19:32:42 - Request: /snackatlas/api/products.php?t=1751225562969
2025-06-29 19:32:42 - Parameters: page=1, search=, filter=
2025-06-29 19:32:42 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:32:42 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:32:42 - Success: Found 9 products, total: 13
2025-06-29 19:33:53 - Request: /snackatlas/api/products.php?t=1751225633835
2025-06-29 19:33:53 - Parameters: page=1, search=, filter=
2025-06-29 19:33:53 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:33:53 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:33:53 - Success: Found 9 products, total: 13
2025-06-29 19:34:03 - Request: /snackatlas/api/products.php?page=1&filter=name_asc&t=1751225643241
2025-06-29 19:34:03 - Parameters: page=1, search=, filter=name_asc
2025-06-29 19:34:03 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:34:03 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY name ASC LIMIT ? OFFSET ?
2025-06-29 19:34:03 - Success: Found 9 products, total: 13
2025-06-29 19:34:06 - Request: /snackatlas/api/products.php?page=1&filter=name_desc&t=1751225646684
2025-06-29 19:34:06 - Parameters: page=1, search=, filter=name_desc
2025-06-29 19:34:06 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:34:06 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY name DESC LIMIT ? OFFSET ?
2025-06-29 19:34:06 - Success: Found 9 products, total: 13
2025-06-29 19:34:09 - Request: /snackatlas/api/products.php?page=1&t=1751225649182
2025-06-29 19:34:09 - Parameters: page=1, search=, filter=
2025-06-29 19:34:09 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:34:09 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:34:09 - Success: Found 9 products, total: 13
2025-06-29 19:34:14 - Request: /snackatlas/api/products.php?t=1751225654855
2025-06-29 19:34:14 - Parameters: page=1, search=, filter=
2025-06-29 19:34:14 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:34:14 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:34:14 - Success: Found 9 products, total: 13
2025-06-29 19:45:40 - Request: /snackatlas/api/products.php?t=1751226340862
2025-06-29 19:45:40 - Parameters: page=1, search=, filter=
2025-06-29 19:45:40 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:45:40 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:45:40 - Success: Found 9 products, total: 13
2025-06-29 19:46:02 - Request: /snackatlas/api/products.php?page=1&search=sushi&t=1751226362079
2025-06-29 19:46:02 - Parameters: page=1, search=sushi, filter=
2025-06-29 19:46:02 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1 AND (name LIKE ? OR description LIKE ?)
2025-06-29 19:46:02 - Main Query: SELECT * FROM products WHERE 1=1 AND (name LIKE ? OR description LIKE ?) ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:46:02 - Success: Found 1 products, total: 1
2025-06-29 19:46:05 - Request: /snackatlas/api/products.php?page=1&search=sushi&filter=name_asc&t=1751226365645
2025-06-29 19:46:05 - Parameters: page=1, search=sushi, filter=name_asc
2025-06-29 19:46:05 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1 AND (name LIKE ? OR description LIKE ?)
2025-06-29 19:46:05 - Main Query: SELECT * FROM products WHERE 1=1 AND (name LIKE ? OR description LIKE ?) ORDER BY name ASC LIMIT ? OFFSET ?
2025-06-29 19:46:05 - Success: Found 1 products, total: 1
2025-06-29 19:46:08 - Request: /snackatlas/api/products.php?page=1&filter=name_asc&t=1751226368544
2025-06-29 19:46:08 - Parameters: page=1, search=, filter=name_asc
2025-06-29 19:46:08 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:46:08 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY name ASC LIMIT ? OFFSET ?
2025-06-29 19:46:08 - Success: Found 9 products, total: 13
2025-06-29 19:47:59 - Request: /snackatlas/api/products.php?t=1751226479124
2025-06-29 19:47:59 - Parameters: page=1, search=, filter=
2025-06-29 19:47:59 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:47:59 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:47:59 - Success: Found 9 products, total: 13
2025-06-29 19:48:32 - Request: /snackatlas/api/products.php?page=1&search=pret&t=1751226512084
2025-06-29 19:48:32 - Parameters: page=1, search=pret, filter=
2025-06-29 19:48:32 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1 AND (name LIKE ? OR description LIKE ?)
2025-06-29 19:48:32 - Main Query: SELECT * FROM products WHERE 1=1 AND (name LIKE ? OR description LIKE ?) ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:48:32 - Success: Found 1 products, total: 1
2025-06-29 19:48:34 - Request: /snackatlas/api/products.php?page=1&t=1751226514643
2025-06-29 19:48:34 - Parameters: page=1, search=, filter=
2025-06-29 19:48:34 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:48:34 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:48:34 - Success: Found 9 products, total: 13
2025-06-29 19:51:24 - Request: /snackatlas/api/products.php?t=1751226684437
2025-06-29 19:51:24 - Parameters: page=1, search=, filter=
2025-06-29 19:51:24 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:51:24 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:51:24 - Success: Found 9 products, total: 13
2025-06-29 19:53:54 - Request: /snackatlas/api/products.php?t=1751226834734
2025-06-29 19:53:54 - Parameters: page=1, search=, filter=
2025-06-29 19:53:54 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:53:54 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:53:54 - Success: Found 9 products, total: 13
2025-06-29 19:55:28 - Request: /snackatlas/api/products.php?t=1751226928651
2025-06-29 19:55:28 - Parameters: page=1, search=, filter=
2025-06-29 19:55:28 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:55:28 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:55:28 - Success: Found 9 products, total: 13
2025-06-29 19:56:16 - Request: /snackatlas/api/products.php?t=1751226976202
2025-06-29 19:56:16 - Parameters: page=1, search=, filter=
2025-06-29 19:56:16 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:56:16 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:56:16 - Success: Found 9 products, total: 13
2025-06-29 19:57:21 - Request: /snackatlas/api/products.php?t=1751227041013
2025-06-29 19:57:21 - Parameters: page=1, search=, filter=
2025-06-29 19:57:21 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:57:21 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:57:21 - Success: Found 9 products, total: 13
2025-06-29 19:57:49 - Request: /snackatlas/api/products.php?t=1751227069374
2025-06-29 19:57:49 - Parameters: page=1, search=, filter=
2025-06-29 19:57:49 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 19:57:49 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 19:57:49 - Success: Found 9 products, total: 13
2025-06-29 20:03:25 - Request: /snackatlas/api/products.php?t=1751227405727
2025-06-29 20:03:25 - Parameters: page=1, search=, filter=
2025-06-29 20:03:25 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 20:03:25 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 20:03:25 - Success: Found 9 products, total: 13
2025-06-29 20:21:51 - Request: /snackatlas/api/products.php?t=1751228511460
2025-06-29 20:21:51 - Parameters: page=1, search=, filter=
2025-06-29 20:21:51 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 20:21:51 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 20:21:51 - Success: Found 9 products, total: 13
2025-06-29 20:27:50 - Request: /snackatlas/api/products.php?t=1751228870781
2025-06-29 20:27:50 - Parameters: page=1, search=, filter=
2025-06-29 20:27:50 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 20:27:50 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 20:27:50 - Success: Found 9 products, total: 13
2025-06-29 20:30:41 - Request: /snackatlas/api/products.php?t=1751229041377
2025-06-29 20:30:41 - Parameters: page=1, search=, filter=
2025-06-29 20:30:41 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 20:30:41 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 20:30:41 - Success: Found 9 products, total: 13
2025-06-29 20:31:07 - Request: /snackatlas/api/products.php?t=1751229067334
2025-06-29 20:31:07 - Parameters: page=1, search=, filter=
2025-06-29 20:31:07 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 20:31:07 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 20:31:07 - Success: Found 9 products, total: 13
2025-06-29 20:32:28 - Request: /snackatlas/api/products.php?t=1751229148770
2025-06-29 20:32:28 - Parameters: page=1, search=, filter=
2025-06-29 20:32:28 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-29 20:32:28 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-29 20:32:28 - Success: Found 9 products, total: 13
2025-06-30 03:05:17 - Request: /snackatlas/api/products.php?t=1751252716976
2025-06-30 03:05:17 - Parameters: page=1, search=, filter=
2025-06-30 03:05:17 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 03:05:17 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 03:05:17 - Success: Found 9 products, total: 13
2025-06-30 03:05:49 - Request: /snackatlas/api/products.php?t=1751252749630
2025-06-30 03:05:49 - Parameters: page=1, search=, filter=
2025-06-30 03:05:49 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 03:05:49 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 03:05:49 - Success: Found 9 products, total: 13
2025-06-30 03:06:09 - Request: /snackatlas/api/products.php?t=1751252769069
2025-06-30 03:06:09 - Parameters: page=1, search=, filter=
2025-06-30 03:06:09 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 03:06:09 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 03:06:09 - Success: Found 9 products, total: 13
2025-06-30 03:12:25 - Request: /snackatlas/api/products.php?t=1751253145915
2025-06-30 03:12:25 - Parameters: page=1, search=, filter=
2025-06-30 03:12:25 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 03:12:25 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 03:12:25 - Success: Found 9 products, total: 13
2025-06-30 03:22:35 - Request: /snackatlas/api/products.php?t=1751253755221
2025-06-30 03:22:35 - Parameters: page=1, search=, filter=
2025-06-30 03:22:35 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 03:22:35 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 03:22:35 - Success: Found 9 products, total: 13
2025-06-30 03:22:43 - Request: /snackatlas/api/products.php?t=1751253763872
2025-06-30 03:22:43 - Parameters: page=1, search=, filter=
2025-06-30 03:22:43 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 03:22:43 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 03:22:43 - Success: Found 9 products, total: 13
2025-06-30 05:48:57 - Request: /api/products.php?t=1751255336954
2025-06-30 05:48:57 - Parameters: page=1, search=, filter=
2025-06-30 05:48:57 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 05:48:57 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 05:48:57 - Success: Found 9 products, total: 13
2025-06-30 05:51:37 - Request: /api/products.php?t=1751255497488
2025-06-30 05:51:37 - Parameters: page=1, search=, filter=
2025-06-30 05:51:37 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 05:51:37 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 05:51:37 - Success: Found 9 products, total: 13
2025-06-30 05:54:15 - Request: /api/products.php?t=1751255655538
2025-06-30 05:54:15 - Parameters: page=1, search=, filter=
2025-06-30 05:54:15 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 05:54:15 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 05:54:15 - Success: Found 9 products, total: 13
2025-06-30 05:54:36 - Request: /api/products.php?t=1751255676082
2025-06-30 05:54:36 - Parameters: page=1, search=, filter=
2025-06-30 05:54:36 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 05:54:36 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 05:54:36 - Success: Found 9 products, total: 13
2025-06-30 05:54:47 - Request: /api/products.php?t=1751255687172
2025-06-30 05:54:47 - Parameters: page=1, search=, filter=
2025-06-30 05:54:47 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 05:54:47 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 05:54:47 - Success: Found 9 products, total: 13
2025-06-30 04:12:07 - Request: /snackatlas/api/products.php?t=1751256727787
2025-06-30 04:12:07 - Parameters: page=1, search=, filter=
2025-06-30 04:12:07 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 04:12:07 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 04:12:07 - Success: Found 9 products, total: 14
2025-06-30 04:12:30 - Request: /snackatlas/api/products.php?t=1751256750774
2025-06-30 04:12:30 - Parameters: page=1, search=, filter=
2025-06-30 04:12:30 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 04:12:30 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 04:12:30 - Success: Found 9 products, total: 12
2025-06-30 04:14:44 - Request: /snackatlas/api/products.php?t=1751256884853
2025-06-30 04:14:44 - Parameters: page=1, search=, filter=
2025-06-30 04:14:44 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 04:14:44 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 04:14:44 - Success: Found 9 products, total: 12
2025-06-30 08:37:48 - Request: /snackatlas/api/products.php?t=1751272668811
2025-06-30 08:37:48 - Parameters: page=1, search=, filter=
2025-06-30 08:37:48 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 08:37:48 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 08:37:48 - Success: Found 9 products, total: 12
2025-06-30 09:11:27 - Request: /snackatlas/api/products.php?t=1751274686964
2025-06-30 09:11:27 - Parameters: page=1, search=, filter=
2025-06-30 09:11:27 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 09:11:27 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 09:11:27 - Success: Found 9 products, total: 12
2025-06-30 09:12:47 - Request: /snackatlas/api/products.php?t=1751274767213
2025-06-30 09:12:47 - Parameters: page=1, search=, filter=
2025-06-30 09:12:47 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 09:12:47 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 09:12:47 - Success: Found 9 products, total: 12
2025-06-30 09:12:51 - Request: /snackatlas/api/products.php?t=1751274771792
2025-06-30 09:12:51 - Parameters: page=1, search=, filter=
2025-06-30 09:12:51 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 09:12:51 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 09:12:51 - Success: Found 9 products, total: 12
2025-06-30 09:12:53 - Request: /snackatlas/api/products.php?t=1751274773174
2025-06-30 09:12:53 - Parameters: page=1, search=, filter=
2025-06-30 09:12:53 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 09:12:53 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 09:12:53 - Success: Found 9 products, total: 12
2025-06-30 09:12:58 - Request: /snackatlas/api/products.php?t=1751274778124
2025-06-30 09:12:58 - Parameters: page=1, search=, filter=
2025-06-30 09:12:58 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 09:12:58 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 09:12:58 - Success: Found 9 products, total: 12
2025-06-30 09:15:03 - Request: /snackatlas/api/products.php?t=1751274903770
2025-06-30 09:15:03 - Parameters: page=1, search=, filter=
2025-06-30 09:15:03 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-06-30 09:15:03 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-06-30 09:15:03 - Success: Found 9 products, total: 12
2025-07-28 16:56:44 - Request: /snackatlas/api/products.php?t=123456
2025-07-28 16:56:44 - Parameters: page=1, search=, filter=
2025-07-28 16:56:44 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-07-28 16:56:44 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-07-28 16:56:44 - Success: Found 9 products, total: 12
2025-07-28 16:56:51 - Request: /snackatlas/api/products.php?t=123456
2025-07-28 16:56:51 - Parameters: page=1, search=, filter=
2025-07-28 16:56:51 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-07-28 16:56:51 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-07-28 16:56:51 - Success: Found 9 products, total: 12
2025-07-28 16:57:22 - Request: /snackatlas/api/products.php?t=123456
2025-07-28 16:57:22 - Parameters: page=1, search=, filter=
2025-07-28 16:57:22 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-07-28 16:57:22 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-07-28 16:57:22 - Success: Found 9 products, total: 12
2025-07-28 17:03:55 - Request: /snackatlas/api/products.php?t=123456
2025-07-28 17:03:55 - Parameters: page=1, search=, filter=
2025-07-28 17:03:55 - Count Query: SELECT COUNT(*) as total FROM products WHERE 1=1
2025-07-28 17:03:55 - Main Query: SELECT * FROM products WHERE 1=1 ORDER BY id ASC LIMIT ? OFFSET ?
2025-07-28 17:03:55 - Success: Found 9 products, total: 12
