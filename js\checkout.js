// Real-time clock functionality
function updateClock() {
  const now = new Date();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  const ampm = hours >= 12 ? 'PM' : 'AM';
  const displayHours = hours % 12 || 12;
  const displayMinutes = minutes.toString().padStart(2, '0');
  
  const timeString = `${displayHours}:${displayMinutes} ${ampm}`;
  document.getElementById('clock').textContent = timeString;
}

// Update clock immediately and then every second
updateClock();
setInterval(updateClock, 1000);

// DOM elements
const orderSummaryHeader = document.querySelector('.order-summary-header');
const orderSummaryContent = document.getElementById('orderSummaryContent');
const toggleIcon = document.getElementById('toggleIcon');
const paymentForm = document.getElementById('paymentForm');
const successModal = document.getElementById('successModal');
const orderNumber = document.getElementById('orderNumber');
const realTimeClock = document.getElementById('realTimeClock');


// Form elements
const cardName = document.getElementById('cardName');
const cardNumber = document.getElementById('cardNumber');
const expiryDate = document.getElementById('expiryDate');
const cvv = document.getElementById('cvv');
const billingAddress = document.getElementById('billingAddress');

// Error message elements
const cardNameError = document.getElementById('cardNameError');
const cardNumberError = document.getElementById('cardNumberError');
const expiryError = document.getElementById('expiryError');
const cvvError = document.getElementById('cvvError');
const addressError = document.getElementById('addressError');

// Order summary toggle functionality
function toggleOrderSummary() {
    const isCollapsed = orderSummaryContent.classList.contains('collapsed');
    
    if (isCollapsed) {
        orderSummaryContent.classList.remove('collapsed');
        toggleIcon.textContent = '−';
        toggleIcon.classList.remove('collapsed');
    } else {
        orderSummaryContent.classList.add('collapsed');
        toggleIcon.textContent = '+';
        toggleIcon.classList.add('collapsed');
    }
}

// Card number formatting
cardNumber.addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
    let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value; // Add spaces every 4 digits
    
    if (formattedValue.length <= 19) { // Max length with spaces
        e.target.value = formattedValue;
    }
});

// Expiry date formatting
expiryDate.addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
    
    if (value.length >= 2) {
        value = value.substring(0, 2) + '/' + value.substring(2, 4);
    }
    
    e.target.value = value;
});

// CVV input restriction
cvv.addEventListener('input', function(e) {
    e.target.value = e.target.value.replace(/\D/g, ''); // Only allow digits
});

// Form validation functions
function validateCardName() {
    const name = cardName.value.trim();
    if (name.length < 2) {
        cardNameError.textContent = 'Please enter a valid cardholder name';
        return false;
    }
    cardNameError.textContent = '';
    return true;
}

function validateCardNumber() {
    const number = cardNumber.value.replace(/\s/g, ''); // Remove spaces
    if (number.length !== 16 || !/^\d{16}$/.test(number)) {
        cardNumberError.textContent = 'Please enter a valid 16-digit card number';
        return false;
    }
    cardNumberError.textContent = '';
    return true;
}

function validateExpiryDate() {
    const expiry = expiryDate.value;
    const expiryRegex = /^(0[1-9]|1[0-2])\/\d{2}$/;
    
    if (!expiryRegex.test(expiry)) {
        expiryError.textContent = 'Please enter a valid expiry date (MM/YY)';
        return false;
    }
    
    // Check if the date is not in the past
    const [month, year] = expiry.split('/');
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear() % 100; // Get last 2 digits
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
    
    const expiryYear = parseInt(year);
    const expiryMonth = parseInt(month);
    
    if (expiryYear < currentYear || (expiryYear === currentYear && expiryMonth < currentMonth)) {
        expiryError.textContent = 'Card has expired';
        return false;
    }
    
    expiryError.textContent = '';
    return true;
}

function validateCVV() {
    const cvvValue = cvv.value;
    if (cvvValue.length !== 3 || !/^\d{3}$/.test(cvvValue)) {
        cvvError.textContent = 'Please enter a valid 3-digit CVV';
        return false;
    }
    cvvError.textContent = '';
    return true;
}

function validateBillingAddress() {
    const address = billingAddress.value.trim();
    if (address.length < 10) {
        addressError.textContent = 'Please enter a complete billing address';
        return false;
    }
    addressError.textContent = '';
    return true;
}

// Real-time validation
cardName.addEventListener('blur', validateCardName);
cardNumber.addEventListener('blur', validateCardNumber);
expiryDate.addEventListener('blur', validateExpiryDate);
cvv.addEventListener('blur', validateCVV);
billingAddress.addEventListener('blur', validateBillingAddress);

// Form submission
paymentForm.addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Validate all fields
    const isCardNameValid = validateCardName();
    const isCardNumberValid = validateCardNumber();
    const isExpiryValid = validateExpiryDate();
    const isCVVValid = validateCVV();
    const isAddressValid = validateBillingAddress();
    
    // If all validations pass, process payment
    if (isCardNameValid && isCardNumberValid && isExpiryValid && isCVVValid && isAddressValid) {
        processPayment();
    } else {
        // Scroll to first error
        const firstError = document.querySelector('.error-message:not(:empty)');
        if (firstError) {
            firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }
});

// Process payment function
function processPayment() {
    const payButton = document.getElementById('payButton');
    
    // Disable button and show processing state
    payButton.disabled = true;
    payButton.textContent = 'Processing Payment...';
    
    // Simulate payment processing
    setTimeout(() => {
        // Generate random order number
        const randomOrderNumber = 'ORD-' + new Date().getFullYear() + '-' + 
                                 String(Math.floor(Math.random() * 1000) + 1).padStart(3, '0');
        orderNumber.textContent = randomOrderNumber;
        
        // Show success modal
        showSuccessModal();
        
        // Reset button state
        payButton.disabled = false;
        payButton.textContent = 'Complete Payment - $125.82';
        
        // Clear form
        paymentForm.reset();
        clearErrorMessages();
        
    }, 2000); // 2 second delay to simulate processing
}

// Clear all error messages
function clearErrorMessages() {
    const errorElements = document.querySelectorAll('.error-message');
    errorElements.forEach(element => {
        element.textContent = '';
    });
}

// Show success modal
function showSuccessModal() {
    successModal.classList.add('show');
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
}

// Close success modal
function closeSuccessModal() {
    successModal.classList.remove('show');
    document.body.style.overflow = 'auto'; // Restore scrolling
    
    // Optionally redirect to a confirmation page or back to shopping
    // window.location.href = 'index.html';
}

// Back to cart function
function backToCart() {
    window.location.href = 'cart.html';
}

// Close modal when clicking outside
successModal.addEventListener('click', function(e) {
    if (e.target === successModal) {
        closeSuccessModal();
    }
});

// Keyboard navigation for modal
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && successModal.classList.contains('show')) {
        closeSuccessModal();
    }
});

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Focus on first input field
    cardName.focus();
    
    // Add smooth scrolling for better UX
    document.documentElement.style.scrollBehavior = 'smooth';
});

// Add visual feedback for form interactions
const formInputs = document.querySelectorAll('input, textarea');
formInputs.forEach(input => {
    input.addEventListener('focus', function() {
        this.parentElement.classList.add('focused');
    });
    
    input.addEventListener('blur', function() {
        this.parentElement.classList.remove('focused');
    });
});

// Prevent form submission on Enter key in input fields (except submit button)
formInputs.forEach(input => {
    if (input.type !== 'submit') {
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                // Move to next input field
                const inputs = Array.from(formInputs);
                const currentIndex = inputs.indexOf(input);
                const nextInput = inputs[currentIndex + 1];
                if (nextInput) {
                    nextInput.focus();
                }
            }
        });
    }
});

// Add loading animation to pay button
function addLoadingAnimation() {
    const payButton = document.getElementById('payButton');
    payButton.innerHTML = `
        <span class="loading-spinner"></span>
        Processing Payment...
    `;
}

// Security feature: Clear sensitive data on page unload
window.addEventListener('beforeunload', function() {
    cardNumber.value = '';
    cvv.value = '';
});

console.log('Checkout page initialized successfully!');