console.log("Testing simple JSON...");
fetch('api/test.php')
    .then(response => response.text())
    .then(text => {
        console.log('Test response:', text);
        try {
            const json = JSON.parse(text);
            console.log('Parsed JSON:', json);
        } catch (e) {
            console.error('Failed to parse JSON:', e);
        }
    })
    .catch(error => console.error('Test fetch error:', error));
