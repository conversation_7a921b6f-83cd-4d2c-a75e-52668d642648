<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// Database configuration
$host = 'localhost';
$dbname = 'group4_db';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get user ID from query parameter
    $userId = $_GET['user_id'] ?? null;
    
    if (!$userId) {
        echo json_encode(['success' => false, 'message' => 'User ID is required']);
        exit;
    }
    
    // Check if wishlist table exists, create if not
    $checkTable = $pdo->query("SHOW TABLES LIKE 'wishlist'");
    if ($checkTable->rowCount() == 0) {
        // Create wishlist table
        $createTable = "CREATE TABLE `wishlist` (
            `id` int NOT NULL AUTO_INCREMENT,
            `user_id` int NOT NULL,
            `product_id` varchar(20) NOT NULL,
            `added_at` datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_user_product` (`user_id`, `product_id`),
            INDEX `idx_user_id` (`user_id`),
            INDEX `idx_product_id` (`product_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createTable);
    }
    
    // Get user's wishlist with product details
    $sql = "SELECT 
                w.id as wishlist_id,
                w.user_id,
                w.product_id,
                w.added_at,
                p.name as product_name,
                p.description,
                p.price,
                p.image,
                p.category
            FROM wishlist w
            JOIN products p ON w.product_id = p.id
            WHERE w.user_id = ?
            ORDER BY w.added_at DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$userId]);
    
    $wishlist = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'wishlist' => $wishlist,
        'count' => count($wishlist)
    ]);
    
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>
