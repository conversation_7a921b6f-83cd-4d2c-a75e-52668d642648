<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Database configuration
$host = 'localhost'; // This should work for both localhost and 127.0.0.1
$dbname = 'group4_db';
$username = 'root';
$password = '';

try {
    // Create database connection with error handling
    $conn = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get pagination parameters
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 9;
    $offset = ($page - 1) * $limit;
    $search = isset($_GET['search']) ? $_GET['search'] : '';
    $filter = isset($_GET['filter']) ? $_GET['filter'] : '';
    
    // Build query
    $query = "SELECT id, name, description, price, image FROM products";
    $countQuery = "SELECT COUNT(*) as total FROM products";
    $params = [];
    
    // Add search condition if provided
    if (!empty($search)) {
        $query .= " WHERE (name LIKE :search OR description LIKE :search)";
        $countQuery .= " WHERE (name LIKE :search OR description LIKE :search)";
        $params[':search'] = "%$search%";
    }
    
    // Add sorting based on filter
    if (!empty($filter)) {
        switch ($filter) {
            case 'name_asc':
                $query .= " ORDER BY name ASC";
                break;
            case 'name_desc':
                $query .= " ORDER BY name DESC";
                break;
            case 'price_asc':
                $query .= " ORDER BY price ASC";
                break;
            case 'price_desc':
                $query .= " ORDER BY price DESC";
                break;
        }
    } else {
        $query .= " ORDER BY id ASC";
    }
    
    // Add pagination
    $query .= " LIMIT :limit OFFSET :offset";
    
    // Prepare and execute count query
    $countStmt = $conn->prepare($countQuery);
    foreach ($params as $key => $value) {
        $countStmt->bindValue($key, $value);
    }
    $countStmt->execute();
    $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    $totalPages = ceil($totalCount / $limit);
    
    // Prepare and execute main query
    $stmt = $conn->prepare($query);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Return as JSON
    echo json_encode([
        'success' => true,
        'products' => $products,
        'pagination' => [
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'totalItems' => $totalCount,
            'limit' => $limit
        ]
    ]);
    
} catch (PDOException $e) {
    // Return error as JSON
    echo json_encode([
        'success' => false,
        'message' => "Database error: " . $e->getMessage()
    ]);
}
?>

