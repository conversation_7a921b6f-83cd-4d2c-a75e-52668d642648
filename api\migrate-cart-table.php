<?php
/**
 * Cart Table Migration Script
 * This script will update the cart table to include product_image and product_price columns
 */

header('Content-Type: application/json');

$host = 'localhost';
$dbname = 'group4_db';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $results = [];
    
    // Step 1: Check current table structure
    $results[] = "Checking current cart table structure...";
    $stmt = $pdo->query("DESCRIBE cart");
    $currentColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasImageColumn = false;
    $hasPriceColumn = false;
    
    foreach ($currentColumns as $column) {
        if ($column['Field'] === 'product_image') {
            $hasImageColumn = true;
        }
        if ($column['Field'] === 'product_price') {
            $hasPriceColumn = true;
        }
    }
    
    // Step 2: Add missing columns
    if (!$hasImageColumn) {
        $results[] = "Adding product_image column...";
        $pdo->exec("ALTER TABLE `cart` ADD COLUMN `product_image` varchar(255) DEFAULT NULL AFTER `product_name`");
        $results[] = "✓ product_image column added successfully";
    } else {
        $results[] = "✓ product_image column already exists";
    }
    
    if (!$hasPriceColumn) {
        $results[] = "Adding product_price column...";
        $pdo->exec("ALTER TABLE `cart` ADD COLUMN `product_price` decimal(10,2) NOT NULL DEFAULT 0.00 AFTER `product_image`");
        $results[] = "✓ product_price column added successfully";
    } else {
        $results[] = "✓ product_price column already exists";
    }
    
    // Step 3: Add indexes if they don't exist
    $results[] = "Adding indexes...";
    try {
        $pdo->exec("ALTER TABLE `cart` ADD INDEX `idx_product_id` (`product_id`)");
        $results[] = "✓ idx_product_id index added";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            $results[] = "✓ idx_product_id index already exists";
        } else {
            throw $e;
        }
    }
    
    try {
        $pdo->exec("ALTER TABLE `cart` ADD INDEX `idx_added_at` (`added_at`)");
        $results[] = "✓ idx_added_at index added";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            $results[] = "✓ idx_added_at index already exists";
        } else {
            throw $e;
        }
    }
    
    // Step 4: Update existing cart records with product information
    $results[] = "Updating existing cart records with product information...";
    $updateStmt = $pdo->prepare("
        UPDATE cart c 
        JOIN products p ON c.product_id = p.id 
        SET 
            c.product_image = p.image,
            c.product_price = p.price
        WHERE c.product_image IS NULL OR c.product_price = 0.00
    ");
    $updateStmt->execute();
    $updatedRows = $updateStmt->rowCount();
    $results[] = "✓ Updated $updatedRows cart records with product information";
    
    // Step 5: Verify the migration
    $results[] = "Verifying migration...";
    $verifyStmt = $pdo->query("
        SELECT 
            COUNT(*) as total_items,
            COUNT(CASE WHEN product_image IS NOT NULL THEN 1 END) as items_with_image,
            COUNT(CASE WHEN product_price > 0 THEN 1 END) as items_with_price
        FROM cart
    ");
    $verification = $verifyStmt->fetch(PDO::FETCH_ASSOC);
    
    $results[] = "✓ Total cart items: " . $verification['total_items'];
    $results[] = "✓ Items with image: " . $verification['items_with_image'];
    $results[] = "✓ Items with price: " . $verification['items_with_price'];
    
    // Step 6: Show updated table structure
    $results[] = "Updated table structure:";
    $stmt = $pdo->query("DESCRIBE cart");
    $newColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($newColumns as $column) {
        $results[] = "  - " . $column['Field'] . " (" . $column['Type'] . ")";
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Cart table migration completed successfully',
        'details' => $results
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Migration failed: ' . $e->getMessage(),
        'details' => $results ?? []
    ]);
}
?>
