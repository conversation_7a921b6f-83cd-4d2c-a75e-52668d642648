<?php
session_start();
include 'db_connection.php';

$username = $_POST['username'];
$password = $_POST['password'];

$sql = "SELECT * FROM admins WHERE username = ?";
$stmt = $conn->prepare($sql);

if (!$stmt) {
    die("SQL error: " . $conn->error);
}

$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();
$admin = $result->fetch_assoc();

if ($admin && password_verify($password, $admin['password'])) {
    $_SESSION['admin'] = $admin['username'];
    $_SESSION['admin_logged_in'] = true;
    header("Location: admin.php");
    exit;
} else {
    echo "<script>alert('Invalid login. Please try again.'); window.location.href='login_admin.php';</script>";
}

$stmt->close();
$conn->close();
?>
