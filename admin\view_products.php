<?php
include 'db_connection.php';
$result = $conn->query("SELECT * FROM products");

if(!$result){
    die("Query failed: " . $conn->error);
}
?>
<!DOCTYPE html>
<html>
<head><title>View Products</title></head>
<link rel="stylesheet" href="style.css">
<body>
    <h1>Product List</h1>

<a href="admin.php" style="position: absolute; top: 30px; left: 60px; text-decoration: none;">
        <button style="background-color: #5e4b3c; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-weight: bold; cursor: pointer;"
         onmouseover="this.style.backgroundColor='#fce8d7'; this.style.color='#5e4b3c' ;" 
        onmouseout="this.style.backgroundColor='#5e4b3c'; this.style.color='white';">Back to Admin Panel</button>
    </a>
</a>

    <table border="1">
        <tr>
            <th>ID</th><th>Name</th><th>Description</th><th>Price</th><th>Image</th><th>Ingredients</th>
            <th>Nutrition</th><th>Category</th><th>Stock</th><th>Actions</th>
        </tr>
        <?php while($row = $result->fetch_assoc()) { ?>
        <tr>
            <td><?= $row['id'] ?></td>
            <td><?= $row['name'] ?></td>
            <td><?= $row['description'] ?></td>
            <td>RM <?= $row['price'] ?></td>
            <td><img src="uploads/<?= $row['image'] ?>" width="50"></td>
            <td><?= $row['ingredients'] ?></td>
            <td><?= $row['nutrition'] ?></td>
            <td><?= $row['category'] ?></td>
            <td><?= $row['stock_quantity'] ?></td>
            <td>
                <a href="update_product.php?id=<?= $row['id'] ?>"><button>Edit</button></a> |
                <a href="delete_product.php?id=<?= $row['id'] ?>" onclick="return confirm('Delete this product?');"><button>Delete</button></a>
            </td>
        </tr>
        <?php } ?>
    </table>
</body>
</html>
<?php $conn->close(); ?>
