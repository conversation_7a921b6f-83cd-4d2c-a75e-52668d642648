// DOM Elements
const userNameSpan = document.getElementById('userName');
const logoutBtn = document.getElementById('logoutBtn');
const startShoppingBtn = document.getElementById('startShoppingBtn');
const exploreBtn = document.getElementById('exploreBtn');
const finalCtaBtn = document.getElementById('finalCtaBtn');
const categoryCards = document.querySelectorAll('.category-card');

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    setupEventListeners();
    addScrollAnimations();
});

// Initialize page with user data
function initializePage() {
    // Check if user is logged in
    const currentUser = localStorage.getItem('currentSnackAtlasUser');
    
    if (!currentUser) {
        // Redirect to login if not logged in
        window.location.href = 'login.html';
        return;
    }
    
    // Parse user data and display welcome message
    try {
        const userData = JSON.parse(currentUser);
        const displayName = userData.firstName || userData.username || 'Food Explorer';
        userNameSpan.textContent = displayName;
    } catch (error) {
        console.error('Error parsing user data:', error);
        userNameSpan.textContent = 'Guest';
    }
    
    // Add entrance animations
    setTimeout(() => {
        document.querySelector('.hero-content').classList.add('animate-slide-up');
    }, 200);
}

// Setup event listeners
function setupEventListeners() {
    // Logout functionality
    logoutBtn.addEventListener('click', handleLogout);
    
    // Shopping buttons
    startShoppingBtn.addEventListener('click', handleStartShopping);
    exploreBtn.addEventListener('click', handleExplore);
    finalCtaBtn.addEventListener('click', handleStartShopping);
    
    // Category cards
    categoryCards.forEach(card => {
        card.addEventListener('click', function() {
            const category = this.dataset.category;
            handleCategoryClick(category);
        });
        
        // Add hover sound effect simulation
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Add click effects to all buttons
    const buttons = document.querySelectorAll('button, .cta-button');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
            `;
            
            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

// Handle logout
function handleLogout() {
    // Show confirmation dialog
    const confirmLogout = confirm('Are you sure you want to logout?');
    
    if (confirmLogout) {
        // Add logout animation
        document.body.style.opacity = '0';
        document.body.style.transition = 'opacity 0.5s ease';
        
        setTimeout(() => {
            // Clear user data
            localStorage.removeItem('currentSnackAtlasUser');
            
            // Redirect to login page
            window.location.href = 'login.html';
        }, 500);
    }
}

// Handle start shopping
function handleStartShopping() {
    // Add loading state
    const button = event.target.closest('button') || event.target;
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
    button.disabled = true;
    
    // Simulate loading and redirect
    setTimeout(() => {
        // In a real application, this would redirect to homepage.html
        // For now, we'll show an alert
        alert('Redirecting to shopping page (homepage.html)...\n\nNote: This is a demo. In a real application, this would navigate to your shopping page.');
        
        // Reset button
        button.innerHTML = originalText;
        button.disabled = false;
        
        // Uncomment the line below when you have homepage.html ready
        // window.location.href = 'homepage.html';
    }, 1500);
}

// Handle explore button
function handleExplore() {
    // Smooth scroll to categories section
    const categoriesSection = document.querySelector('.categories');
    if (categoriesSection) {
        categoriesSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
        
        // Add highlight animation to categories
        setTimeout(() => {
            categoryCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.animation = 'fadeInScale 0.6s ease-out forwards';
                }, index * 100);
            });
        }, 500);
    }
}

// Handle category clicks
function handleCategoryClick(category) {
    // Add click animation
    const clickedCard = event.target.closest('.category-card');
    clickedCard.style.transform = 'scale(0.95)';
    
    setTimeout(() => {
        clickedCard.style.transform = '';
        
        // Show category selection message
        const categoryNames = {
            'asian': 'Asian Delights',
            'european': 'European Classics',
            'american': 'American Favorites',
            'latin': 'Latin Spices',
            'african': 'African Treasures',
            'middle-eastern': 'Middle Eastern'
        };
        
        const categoryName = categoryNames[category] || 'Selected Category';
        
        // Create a temporary notification
        showNotification(`Exploring ${categoryName}! Redirecting to shopping...`);
        
        // In a real application, this would filter products or navigate to a category page
        setTimeout(() => {
            alert(`Redirecting to ${categoryName} collection...\n\nNote: This is a demo. In a real application, this would show filtered products.`);
        }, 1000);
    }, 150);
}

// Show notification
function showNotification(message) {
    // Remove existing notification
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
        color: white;
        padding: 15px 25px;
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(139, 69, 19, 0.3);
        z-index: 1001;
        font-weight: 500;
        transform: translateX(400px);
        transition: transform 0.4s ease;
        max-width: 300px;
    `;
    
    notification.innerHTML = `
        <i class="fas fa-info-circle" style="margin-right: 10px;"></i>
        ${message}
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(400px)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 400);
    }, 3000);
}

// Add scroll animations
function addScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-slide-up');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.feature-card, .category-card, .cta-content');
    animateElements.forEach(el => {
        observer.observe(el);
    });
}

// Add CSS for ripple animation
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .notification {
        font-family: 'Poppins', sans-serif;
    }
`;
document.head.appendChild(style);

// Add parallax effect to floating elements
window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const parallaxElements = document.querySelectorAll('.float-item');
    
    parallaxElements.forEach((element, index) => {
        const speed = 0.5 + (index * 0.1);
        const yPos = -(scrolled * speed);
        element.style.transform = `translateY(${yPos}px) rotate(${scrolled * 0.02}deg)`;
    });
});

// Add navbar scroll effect
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    
    if (window.scrollY > 100) {
        navbar.style.background = 'rgba(255, 255, 255, 0.98)';
        navbar.style.boxShadow = '0 4px 20px rgba(139, 69, 19, 0.15)';
    } else {
        navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        navbar.style.boxShadow = '0 2px 20px rgba(139, 69, 19, 0.1)';
    }
});

// Add loading screen
window.addEventListener('load', function() {
    // Hide loading screen if it exists
    const loadingScreen = document.querySelector('.loading-screen');
    if (loadingScreen) {
        loadingScreen.style.opacity = '0';
        setTimeout(() => {
            loadingScreen.remove();
        }, 500);
    }
    
    // Add entrance animation to hero content
    setTimeout(() => {
        const heroContent = document.querySelector('.hero-content');
        if (heroContent) {
            heroContent.style.animation = 'slideInUp 0.8s ease-out forwards';
        }
    }, 300);
});

// Keyboard navigation support
document.addEventListener('keydown', function(e) {
    // ESC key to close notifications
    if (e.key === 'Escape') {
        const notification = document.querySelector('.notification');
        if (notification) {
            notification.style.transform = 'translateX(400px)';
            setTimeout(() => notification.remove(), 400);
        }
    }
    
    // Enter key on focused category cards
    if (e.key === 'Enter' && document.activeElement.classList.contains('category-card')) {
        document.activeElement.click();
    }
});

// Add focus styles for accessibility
const focusStyle = document.createElement('style');
focusStyle.textContent = `
    .category-card:focus,
    .cta-button:focus,
    button:focus {
        outline: 3px solid #8b4513;
        outline-offset: 2px;
    }
    
    .category-card {
        cursor: pointer;
        tabindex: 0;
    }
`;
document.head.appendChild(focusStyle);

// Make category cards focusable
categoryCards.forEach(card => {
    card.setAttribute('tabindex', '0');
    card.setAttribute('role', 'button');
    card.setAttribute('aria-label', `Explore ${card.querySelector('.category-title').textContent}`);
});

console.log('Snack Atlas Main Page Loaded Successfully! 🍪');

// Add performance monitoring
if ('performance' in window) {
    window.addEventListener('load', function() {
        setTimeout(() => {
            const perfData = performance.getEntriesByType('navigation')[0];
            console.log(`Page loaded in ${Math.round(perfData.loadEventEnd - perfData.fetchStart)}ms`);
        }, 0);
    });
}