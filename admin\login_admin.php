<?php session_start(); ?>
<!DOCTYPE html>
<html>
<head>
    <title>Admin Login & Register</title>
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #fdf6ef;
            margin: 0;
            padding: 50px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        h2 {
            color: #5e4b3c;
        }

        .auth-box {
            background-color: #fff;
            border: 2px solid #d2b48c;
            padding: 25px;
            margin: 15px;
            width: 320px;
            border-radius: 12px;
            box-shadow: 2px 4px 12px rgba(0,0,0,0.1);
            text-align: center;
        }

        input {
            width: 90%;
            padding: 10px;
            margin: 8px 0;
            border-radius: 6px;
            border: 1px solid #ccc;
            font-size: 14px;
        }

        input[type="submit"] {
            background-color: #5e4b3c;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s, color 0.3s;
        }

        input[type="submit"]:hover {
            background-color: #fce8d7;
            color: #5e4b3c;
        }

        .note {
            font-size: 0.85em;
            color: #888;
        }

        .toggle-link {
            color: #5e4b3c;
            text-decoration: underline;
            cursor: pointer;
            font-size: 14px;
        }

        .toggle-link:hover {
            color: #a9713d;
        }
    </style>
</head>
<body>

    <div class="auth-box">
        <h2>Admin Login</h2>
        <form action="process_login.php" method="POST">
            <input type="text" name="username" placeholder="Enter username" required><br>
            <input type="password" name="password" placeholder="Enter password" required><br>
            <input type="submit" value="Login">
        </form>

        <p>Don't have an admin account? <span class="toggle-link" onclick="toggleRegister()">Register here</span></p>
    </div>

    <div id="registerBox" class="auth-box" style="display:none;">
        <h2>Register New Admin</h2>
        <form action="process_register.php" method="POST">
            <input type="text" name="username" placeholder="Create username" required><br>
            <input type="password" name="password" placeholder="Create password" required><br>
            <input type="text" name="passcode" placeholder="Enter admin passcode" required><br>
            <span class="note">* Admin passcode required to register.</span><br>
            <input type="submit" value="Register">
        </form>
    </div>

    <a href="main.php"><button style="background-color: #5e4b3c; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-weight: bold; cursor: pointer;"
         onmouseover="this.style.backgroundColor='#fce8d7'; this.style.color='#5e4b3c' ;" 
        onmouseout="this.style.backgroundColor='#5e4b3c'; this.style.color='white';">Back to Main Page</button></a>

    <script>
        function toggleRegister() {
            document.getElementById("registerBox").style.display = "block";
        }
    </script>

</body>
</html>
