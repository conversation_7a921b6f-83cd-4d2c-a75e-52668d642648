<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>SnackAtlas | Profile</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    
    <link rel="icon" type="image/x-icon" href="img/logo.png">
    <link href="css/profile.css" rel="stylesheet" />
  </head>
  <body>
    <div class="app">
        <!-- <img class="bg-image bg-1" alt="Background" src="img/profile-bg.png" /> -->

      <!-- Profile Page -->
      <div id="profilePage" class="page active">
        <div class="profile-container">
          <!-- Background image -->
          <img class="background-image" alt="Background" src="img/profile-bg.png" />

          <!-- Header -->
          <header class="header">
            <div class="nav-left">
                  <img class="logo" alt="Logo" src="img/logo.png" />
            </div>

            <div class="nav-right">
                <div class="nav-icon">
                    <a href="cart.html">
                        <img src="img/cart-icon.svg" alt="cart icon">
                    </a>
                </div>
                <div class="clock" id="clock">12:00 PM</div>
            </div>
          </header>

          <!-- Main content -->
          <main class="main-content">
            <!-- Page title -->
            <h1 class="page-title">My Profile</h1>

            <div class="content-wrapper">
              <!-- Profile section -->
              <div class="profile-section">
                <div class="avatar-container">
                  <img id="profileImage" class="profile-avatar" src="" alt="Profile" style="display: none;" />
                  <div id="avatarPlaceholder" class="avatar-placeholder">
                    <span>profile<br />photo</span>
                  </div>
                </div>

                <div class="profile-info">
                  <div id="displayName" class="profile-name">(NAME)</div>
                  <div class="profile-email"><EMAIL></div>
                </div>
              </div>

              <!-- Menu section -->
              <div class="menu-section">
                <ul class="menu-list">
                  <li class="menu-item" onclick="openEditAccount()">
                    
                    <span class="menu-text">Edit account info</span>
                  </li>
                  <li class="menu-item">
                    <span class="menu-icon"></span>
                    <span class="menu-text">Wishlist</span>
                  </li>
                  <li class="menu-item">
                    <span class="menu-icon"></span>
                    <span class="menu-text">Orders & History</span>
                  </li>
                  <li class="menu-item">
                   
                    <span class="menu-text">Delete Account</span>
                  </li>
                </ul>
              </div>
            </div>
          </main>

          <!-- Footer -->
          <footer class="footer">
            <a href="shop.html">
                <button class="back-button">
                <svg class="back-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="m12 19-7-7 7-7"></path>
                    <path d="M19 12H5"></path>
                </svg>
                Back to Shop
                </button>
            </a>
          </footer>
        </div>
      </div>

      <!-- Edit Account Modal -->
      <div id="editAccountModal" class="modal">
        <div class="modal-content">
          <div class="modal-header">
            <h2>Edit Account Info</h2>
            <button class="close-button" onclick="closeEditAccount()">&times;</button>
          </div>
          
          <div class="modal-body">
            <!-- Profile Picture Upload -->
            <div class="form-group">
              <label for="imageUpload" class="form-label">Profile Picture</label>
              <div class="image-upload-container">
                <div class="current-image">
                  <img id="modalProfileImage" class="modal-avatar" src="" alt="Profile" style="display: none;" />
                  <div id="modalAvatarPlaceholder" class="modal-avatar-placeholder">
                    <span>No Image</span>
                  </div>
                </div>
                <div class="upload-controls">
                  <input type="file" id="imageUpload" accept="image/*" style="display: none;" onchange="handleImageUpload(event)" />
                  <button type="button" class="upload-button" onclick="document.getElementById('imageUpload').click()">
                    Choose Image
                  </button>
                  <button type="button" class="remove-button" onclick="removeImage()" id="removeImageBtn" style="display: none;">
                    Remove Image
                  </button>
                </div>
              </div>
            </div>

            <!-- Name Input -->
            <div class="form-group">
              <label for="nameInput" class="form-label">Name</label>
              <input type="text" id="nameInput" class="form-input" placeholder="Enter your name" />
            </div>

            <!-- Email Display (Read-only) -->
            <div class="form-group">
              <label class="form-label">Email</label>
              <div class="email-display"><EMAIL></div>
            </div>
          </div>

          <div class="modal-footer">
            <button class="cancel-button" onclick="closeEditAccount()">Cancel</button>
            <button class="save-button" onclick="saveAccountInfo()">Save Changes</button>
          </div>
        </div>
      </div>
    </div>

    <script src="js/profile.js"></script>
  </body>
</html>