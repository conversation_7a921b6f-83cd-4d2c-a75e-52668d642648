<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Log request for debugging
$logFile = 'products_api.log';
file_put_contents($logFile, date('Y-m-d H:i:s') . " - Request: " . $_SERVER['REQUEST_URI'] . "\n", FILE_APPEND);

// Database connection
$host = 'localhost';
$dbname = 'group4_db';
$username = 'root';
$password = '';

// Get parameters
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$search = isset($_GET['search']) ? $_GET['search'] : '';
$filter = isset($_GET['filter']) ? $_GET['filter'] : '';
$limit = 9; // Products per page
$offset = ($page - 1) * $limit;

// Log received parameters for debugging
file_put_contents($logFile, date('Y-m-d H:i:s') . " - Parameters: page=$page, search=$search, filter=$filter\n", FILE_APPEND);

try {
    // Create database connection
    $conn = new mysqli($host, $username, $password, $dbname);
    
    // Check connection
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    // Build query
    $query = "SELECT * FROM products WHERE 1=1";
    $countQuery = "SELECT COUNT(*) as total FROM products WHERE 1=1";
    
    $params = [];
    $types = "";
    
    // Add search condition if provided
    if (!empty($search)) {
        $searchTerm = "%$search%";
        $query .= " AND (name LIKE ? OR description LIKE ?)";
        $countQuery .= " AND (name LIKE ? OR description LIKE ?)";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $types .= "ss";
    }
    
    // Add sorting based on filter
    if (!empty($filter)) {
        switch ($filter) {
            case 'name_asc':
                $query .= " ORDER BY name ASC";
                break;
            case 'name_desc':
                $query .= " ORDER BY name DESC";
                break;
            case 'price_asc':
                $query .= " ORDER BY price ASC";
                break;
            case 'price_desc':
                $query .= " ORDER BY price DESC";
                break;
            default:
                // If filter is a category
                $query .= " AND category = ?";
                $countQuery .= " AND category = ?";
                $params[] = $filter;
                $types .= "s";
                break;
        }
    }
    
    // Add default sorting if no filter provided
    if (empty($filter) || in_array($filter, ['name_asc', 'name_desc', 'price_asc', 'price_desc'])) {
        if (!strpos($query, "ORDER BY")) {
            $query .= " ORDER BY id ASC";
        }
    }
    
    // Add pagination
    $query .= " LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    $types .= "ii";
    
    // Log queries for debugging
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Count Query: $countQuery\n", FILE_APPEND);
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Main Query: $query\n", FILE_APPEND);
    
    // Prepare and execute count query
    $countStmt = $conn->prepare($countQuery);
    if (!empty($params) && !empty($types) && strpos($countQuery, "?") !== false) {
        // Only bind parameters for count query if it has placeholders
        $countTypes = substr($types, 0, -2); // Remove the last two characters (for LIMIT and OFFSET)
        $countParams = array_slice($params, 0, -2); // Remove the last two parameters
        
        if (!empty($countTypes) && !empty($countParams)) {
            $countStmt->bind_param($countTypes, ...$countParams);
        }
    }
    $countStmt->execute();
    $countResult = $countStmt->get_result();
    $totalRow = $countResult->fetch_assoc();
    $total = $totalRow['total'];
    
    // Calculate pagination
    $totalPages = ceil($total / $limit);
    
    // Prepare and execute main query
    $stmt = $conn->prepare($query);
    if (!empty($params) && !empty($types)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    $products = [];
    while ($row = $result->fetch_assoc()) {
        $products[] = $row;
    }
    
    // Prepare response
    $response = [
        'success' => true,
        'products' => $products,
        'pagination' => [
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'totalProducts' => $total,
            'limit' => $limit
        ]
    ];
    
    // Log success
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Success: Found " . count($products) . " products, total: $total\n", FILE_APPEND);
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Log error
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Error: " . $e->getMessage() . "\n", FILE_APPEND);
    
    // Return error response
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
?>
