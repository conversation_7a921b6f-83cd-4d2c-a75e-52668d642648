<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>SnackAtlas | Home</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <link rel="icon" type="image/x-icon" href="img/logo.png">
    <link href="css/style.css" rel="stylesheet" />
  </head>
  <body>
    <div class="container">
      <!-- Background Images -->
      <img class="bg-image bg-1" alt="Background" src="img/homepage-bg.png" />
      <img class="bg-image bg-2" alt="Background" src="/bg-2.png" />

      <!-- Navigation Bar -->
      <header class="navbar sticky">
        <div class="nav-left">
          <img class="logo" alt="Logo" src="img/logo.png" />
        </div>

        <nav class="nav-center">
          <ul class="nav-links">
            <li><a href="#home" class="nav-link active">home</a></li>
            <li class="nav-separator">|</li>
            <li><a href="#about" class="nav-link">about</a></li>
            <li class="nav-separator">|</li>
            <li class="dropdown">
              <a href="#" class="nav-link dropdown-toggle">browse</a>
              <div class="dropdown-menu">
                <a href="#shop" class="dropdown-item">featured items</a>
                <a href="shop.html" class="dropdown-item">shop</a>
              </div>
            </li>
            <li class="nav-separator">|</li>
            <li><a href="#contact" class="nav-link">contact us</a></li>
          </ul>
        </nav>

        <div class="nav-right">
            <div class="nav-icon">
                <a href="cart.html">
                    <img src="img/cart-icon.svg" alt="cart icon">
                </a>
            </div>
            <div class="nav-icon">
                <a href="profile.html">  
                    <img src="img/user-icon.svg" alt="user icon">
                </a>
            </div>
            <div class="nav-icon">
                <a href="javascript:void(0)" id="logoutButton">  
                    <img src="img/logout-icon.svg" alt="logout icon">
                </a>
            </div>
          <div class="clock" id="clock">12:00 PM</div>
        </div>
      </header>

      <!-- Hero Section -->
      <section class="hero-section" id="home">
        <div class="hero-content">
          <div class="hero-image-container">
            <img class="hero-image active" alt="Hero Image 1" src="img/hero img.png" />
            <img class="hero-image" alt="Hero Image 2" src="img/hero img 2.jpg" />
            <img class="hero-image" alt="Hero Image 3" src="img/hero img 3.jpg" />
          </div>
          <div class="hero-text">
            <h1 class="hero-title">SnackAtlas</h1>
            <p class="hero-description">
              Where Every Bite Tells a Global Story.
            </p>
            <a href="shop.html">
              <button class="hero-button">Browse Snacks →</button>
            </a>
          </div>
        </div>
      </section>

      <!-- Headline Section -->
      <section class="headline-section" id="discover">
        <h2 class="headline-title">Discover</h2>
        <p class="headline-description">
          Discover unique snacks and treats from every corner of the world. 
          From bold flavors to nostalgic bites, SnackAtlas curates a world tour of taste delivered right to your door.
        </p>
        <div class="quiz-container">
          <button class="quiz-button" onclick="openQuiz()">Take Our Snack Quiz! 🍿</button>
        </div>
      </section>

      <!-- About Section -->
      <section class="about-section" id="about">
        <!-- Top Border -->
        <div class="border-container">
          <img class="border-image" alt="Border" src="img/banner-1.png" />
          <img class="border-image" alt="Border" src="img/banner-1.png" />
        </div>

        <!-- Content -->
        <div class="about-content">
          <img class="about-bg" alt="Background" src="img/about-bg.png" />
          <div class="about-inner">
            <img class="about-image" alt="About Image" src="img/about-img.png" />
            <div class="about-text">
              <h2 class="section-title">About Us</h2>
              <p class="section-description">
                SnackAtlas is your flavor journey. We handpick unique treats from across the globe 
                and bring them to your hands with care. Our mission? To turn every snack break into a little adventure. 
                <br><br>Whether you're craving Korean ramyeon, Japanese mochi, or something sweet and salty in between, <br>we got you.
              </p>
            </div>
          </div>
        </div>

        <!-- Bottom Border -->
        <div class="border-container">
          <img class="border-image" alt="Border" src="img/banner-2.png" />
          <img class="border-image" alt="Border" src="img/banner-2.png" />
        </div>
      </section>

      <!-- Shop Section -->
      <section class="shop-section" id="shop">
        <h2 class="section-title">Featured Items</h2>
        <p class="section-description">
          Browse Our Bestsellers <br>
          Explore the snacks everyone's craving right now.
        </p>

        <!-- Product Carousel -->
        <div class="carousel-container">
          <button class="carousel-btn carousel-prev" id="prevBtn">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="m15 18-6-6 6-6"></path>
            </svg>
          </button>

          <div class="products-container" id="productsContainer">
            <!-- Products will be loaded dynamically from database -->
          </div>

          <button class="carousel-btn carousel-next" id="nextBtn">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="m9 18 6-6-6-6"></path>
            </svg>
          </button>
        </div>

        <div class="shop-button-container">
          <a href="shop.html"><button class="shop-button">Visit the Shop! →</button></a>
        </div>
      </section>

      <!-- Contact Section -->
      <section class="contact-section" id="contact">
        <img class="contact-bg" alt="Contact Background" src="img/contact-bg.png" />
        <div class="contact-content">
          <img class="contact-logo" alt="Logo" src="img/logo.png" />
          <h2 class="section-title">Contact Us</h2>
          
          <!-- Social Links -->
          <div class="contact-social">
            <a href="https://instagram.com/snackatlas" target="_blank" class="social-link">instagram</a>
            <span class="social-separator">|</span>
            <a href="https://facebook.com/snackatlas" target="_blank" class="social-link">facebook</a>
            <span class="social-separator">|</span>
            <a href="https://twitter.com/snackatlas" target="_blank" class="social-link">twitter</a>
          </div>
          
          <!-- Contact Content Container -->
          <div class="contact-main">
            <!-- Google Map -->
            <div class="map-container">
              <iframe 
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3861.2087!2d121.0437!3d14.6760!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3397ca21ac302007%3A0x5b5f8b5c5b5f8b5c!2sSnackAtlas%20Coffee%20Shop!5e0!3m2!1sen!2sph!4v1234567890123!5m2!1sen!2sph"
                width="100%" 
                height="100%" 
                style="border:0;" 
                allowfullscreen="" 
                loading="lazy" 
                referrerpolicy="no-referrer-when-downgrade">
              </iframe>
            </div>
            
            <!-- Contact Form -->
            <div class="contact-form-container">
              <form class="contact-form" id="contactForm">
                <div class="form-group">
                  <label for="fullName">Full Name</label>
                  <input type="text" id="fullName" name="fullName" required>
                </div>
                
                <div class="form-group">
                  <label for="email">Email</label>
                  <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                  <label for="message">Message</label>
                  <textarea id="message" name="message" rows="5" required></textarea>
                </div>
                
                <button type="submit" class="submit-button">Send Message</button>
                
                <!-- Success Message -->
                <div id="contactSuccessMessage" class="contact-success-message" style="display: none;">
                  ✅ Message sent successfully! We'll get back to you soon.
                </div>
              </form>
            </div>
          </div>
          
          <button class="contact-button" onclick="openNewsletter()">Subscribe to our newsletter →</button>
          <p class="contact-footer">made with ♡ by SnackAtlas</p>
        </div>
      </section>
    </div>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop" onclick="scrollToTop()">
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="m18 15-6-6-6 6"></path>
      </svg>
    </button>

    <!-- Quiz Modal -->
    <div class="quiz-modal" id="quizModal">
      <div class="quiz-content">
        <div class="quiz-header">
          <h2>🍿 Snack Personality Quiz</h2>
          <button class="close-quiz" onclick="closeQuiz()">&times;</button>
        </div>
        
        <div class="quiz-body" id="quizBody">
          <!-- Quiz content will be loaded here -->
        </div>
      </div>
    </div>

    <!-- Newsletter Modal -->
    <div class="newsletter-modal" id="newsletterModal">
      <div class="newsletter-content">
        <div class="newsletter-header">
          <h2>📧 Subscribe to Our Newsletter</h2>
          <button class="close-newsletter" onclick="closeNewsletter()">&times;</button>
        </div>
        
        <div class="newsletter-body">
          <p>Get the latest snack updates, exclusive offers, and flavor adventures delivered to your inbox!</p>
          <form class="newsletter-form" id="newsletterForm">
            <input type="email" placeholder="Enter your email" required>
            <button type="submit">Subscribe</button>
          </form>
        </div>
      </div>
    </div>

    <script src="js/script.js"></script>
  </body>
</html>
