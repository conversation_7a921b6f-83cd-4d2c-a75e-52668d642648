<?php
include 'db_connection.php';

if (isset($_GET['id'])) {
    $id = $_GET['id'];
    $result = $conn->query("SELECT * FROM products WHERE id='$id'");
    $product = $result->fetch_assoc();
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $id = $_POST['id'];
    $name = $_POST['name'];
    $description = $_POST['description'];
    $price = $_POST['price'];
    $ingredients = $_POST['ingredients'];
    $nutrition = $_POST['nutrition'];
    $category = $_POST['category'];
    $stock_quantity = $_POST['stock_quantity'];

    // Handle image update
    if (!empty($_FILES['image']['name'])) {
        $image = $_FILES['image']['name'];
        $target = "uploads/" . basename($image);
        move_uploaded_file($_FILES['image']['tmp_name'], $target);
        $conn->query("UPDATE products SET image='$image' WHERE id='$id'");
    }

    $sql = "UPDATE products SET
            name='$name',
            description='$description',
            price='$price',
            ingredients='$ingredients',
            nutrition='$nutrition',
            category='$category',
            stock_quantity='$stock_quantity'
            WHERE id='$id'";

    if ($conn->query($sql) === TRUE) {
        echo "Product updated successfully.<br><a href='view_products.php'>Back to Product List</a>";
    } else {
        echo "Error updating product: " . $conn->error;
    }
}
?>

<?php if (isset($product)) { ?>
<!DOCTYPE html>
<html>
<head><title>Edit Product</title></head>
<link rel="stylesheet" href="style.css">
<body>
    <h1>Edit Product</h1>
    <form action="update_product.php" method="POST" enctype="multipart/form-data">
        <input type="hidden" name="id" value="<?= $product['id'] ?>">
        <input type="text" name="name" value="<?= $product['name'] ?>" required><br>
        <textarea name="description" required><?= $product['description'] ?></textarea><br>
        <input type="number" step="0.01" name="price" value="<?= $product['price'] ?>" required><br>
        <textarea name="ingredients" required><?= $product['ingredients'] ?></textarea><br>
        <textarea name="nutrition" required><?= $product['nutrition'] ?></textarea><br>
        <input type="text" name="category" value="<?= $product['category'] ?>" required><br>
        <input type="number" name="stock_quantity" value="<?= $product['stock_quantity'] ?>" required><br>
        <p>Current image: <img src="uploads/<?= $product['image'] ?>" width="50"></p>
        <input type="file" name="image"><br>
        <input type="submit" value="Update Product">
    </form>
</body>
</html>
<?php } ?>
