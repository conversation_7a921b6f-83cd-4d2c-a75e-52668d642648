<?php
echo "<h1>Implementation Verification</h1>";

// Check if all required files exist
$requiredFiles = [
    'js/shop.js' => 'Shop JavaScript with wishlist functionality',
    'js/profile.js' => 'Profile JavaScript with wishlist and delete account',
    'api/get_wishlist.php' => 'Get wishlist API endpoint',
    'api/toggle_wishlist.php' => 'Toggle wishlist API endpoint', 
    'api/delete_user.php' => 'Delete user API endpoint',
    'css/profile.css' => 'Profile CSS with wishlist styles'
];

echo "<h2>File Verification:</h2>";
foreach ($requiredFiles as $file => $description) {
    if (file_exists($file)) {
        echo "<p>✓ <strong>$file</strong> - $description</p>";
    } else {
        echo "<p>✗ <strong>$file</strong> - $description (MISSING)</p>";
    }
}

// Check database connection and tables
echo "<h2>Database Verification:</h2>";
try {
    $conn = new mysqli('localhost', 'root', '', 'group4_db');
    
    if ($conn->connect_error) {
        echo "<p>✗ Database connection failed</p>";
    } else {
        echo "<p>✓ Database connection successful</p>";
        
        // Check required tables
        $tables = ['users', 'products', 'wishlist'];
        foreach ($tables as $table) {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            if ($result->num_rows > 0) {
                echo "<p>✓ Table '$table' exists</p>";
            } else {
                echo "<p>✗ Table '$table' missing</p>";
            }
        }
    }
    $conn->close();
} catch (Exception $e) {
    echo "<p>✗ Database error: " . $e->getMessage() . "</p>";
}

// Check key functionality in files
echo "<h2>Code Verification:</h2>";

// Check shop.js for wishlist functions
if (file_exists('js/shop.js')) {
    $shopJs = file_get_contents('js/shop.js');
    $shopChecks = [
        'toggleWishlist' => 'Wishlist toggle function',
        'loadUserWishlist' => 'Load user wishlist function',
        'updateWishlistUI' => 'Update wishlist UI function',
        'heart-btn' => 'Heart button in product cards'
    ];
    
    foreach ($shopChecks as $check => $description) {
        if (strpos($shopJs, $check) !== false) {
            echo "<p>✓ Shop.js contains: $description</p>";
        } else {
            echo "<p>✗ Shop.js missing: $description</p>";
        }
    }
}

// Check profile.js for wishlist and delete functions
if (file_exists('js/profile.js')) {
    $profileJs = file_get_contents('js/profile.js');
    $profileChecks = [
        'openWishlistModal' => 'Open wishlist modal function',
        'removeFromWishlist' => 'Remove from wishlist function',
        'openDeleteAccountModal' => 'Open delete account modal function',
        'confirmDeleteAccount' => 'Confirm delete account function'
    ];
    
    foreach ($profileChecks as $check => $description) {
        if (strpos($profileJs, $check) !== false) {
            echo "<p>✓ Profile.js contains: $description</p>";
        } else {
            echo "<p>✗ Profile.js missing: $description</p>";
        }
    }
}

echo "<h2>Implementation Summary:</h2>";
echo "<ul>";
echo "<li><strong>Wishlist Like Buttons:</strong> Added to product cards in shop page with heart icon SVG</li>";
echo "<li><strong>Wishlist Database:</strong> Created wishlist table with user_id, product_id, and timestamps</li>";
echo "<li><strong>Wishlist API:</strong> get_wishlist.php and toggle_wishlist.php endpoints for CRUD operations</li>";
echo "<li><strong>Profile Wishlist:</strong> Modal displaying saved products with image, name, description, price</li>";
echo "<li><strong>Remove from Wishlist:</strong> Functionality to remove items from wishlist</li>";
echo "<li><strong>Delete Account:</strong> Password-confirmed account deletion that updates users table</li>";
echo "<li><strong>UI/UX:</strong> Proper styling, notifications, and responsive design</li>";
echo "</ul>";

echo "<h2>Testing Links:</h2>";
echo "<p><a href='login.html' target='_blank'>Login/Register Page</a></p>";
echo "<p><a href='shop.html' target='_blank'>Shop Page (test heart buttons)</a></p>";
echo "<p><a href='profile.html' target='_blank'>Profile Page (test wishlist & delete account)</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; padding-left: 30px; }
li { margin: 5px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
