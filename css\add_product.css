@import url('https://fonts.googleapis.com/css2?family=UnifrakturCook:wght@700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Epilogue:ital,wght@0,100..900;1,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Pirata+One&display=swap');

* 
{
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  outline: none;
  border: none;
  text-decoration: none;
  font-family: "Epilogue", sans-serif;

}

:root 
{
  --don-juan: #5c4a4a;
  --old-burgundy: #3f3130;
  --color3: #a5a19e;
  --spicy-pink: #836f70;
  --raisin-black: #271e1f;
  --magnolia: #f7f2ff;
  --pinkswan: #c0b5b3;
  --brandy: #ac4c3d;
  --red: #ac1634;
}

html 
{
  font-size: 62.5%;
  overflow-x: hidden;
}

.bg-image
{ 
  background-image: url("../img/homepage-bg.png");
  filter: blur(1px);
  -webkit-filter: blur(1px);
  width: 100%; 
  height: 100%;
  position: fixed;
  background-position: center;
  background-size: cover;
  z-index:-10;
}

.container 
{
  max-width: 1100px;
  margin: 0 auto;
  height: auto;
  min-height: 100vh;
  padding: 2rem;
  background: rgba(192, 181, 179, 0.53);
  border-radius: 16px;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 2px solid rgba(192, 181, 179, 0.5);
}

.header 
{
  background-color: var(--don-juan);
  position: sticky;
  top: 0;
  left: 0;
  z-index: 1000;
}

.header .header_body 
{
  display: flex;
  align-items: center;
  padding: 2.5rem 2rem;
}

.logo 
{ 
  font-family: "UnifrakturCook", cursive; 
  font-size: 3rem;
  margin-right: auto;
  color: var(--pinkswan);
}

.header .header_body 
{
  margin-right: auto;
  font-size: 2.5rem;
  color: var(--pinkswan);
}

.header .header_body .navbar a 
{
  margin-left: 2rem;
  font-size: 1.9rem;
  color: var(--magnolia);
}

.header .header_body .cart 
{
  margin-left: 2rem;
  font-size: 2rem;
  color: var(--magnolia);
}

.header .header_body .navbar a:hover,
.header .header_body .cart:hover 
{
  color: var(--spicy-pink);
}

.header .header_body .cart span { padding: 0 0.5rem; }

.active-btn { text-decoration: underline; }

#menu-btn 
{
  margin-left: 2rem;
  font-size: 2.5rem;
  cursor: pointer;
  color: var(--magnolia);
  display: none;
}

.display_message 
{
  background-color: var(--don-juan);
  padding: 2.5rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.display_message span { font-size: 1.7rem; }

.display_message i 
{
  font-size: 2.5rem;
  cursor: pointer;
}

.display_message i:hover { color: var(--red); }

.heading, .view-products-heading, .shop-heading, .cart-heading
{
  text-align: center;
  font-size: 6rem;
  letter-spacing: 1.5px;
  font-family: "Pirata One", system-ui;;
  text-transform: uppercase;
  color: var(--don-juan);
}

.heading { padding: 7rem 0 2rem; }

.view-products-heading, .cart-heading, .shop-heading { padding: 0.5rem 0 2rem; }

.divider, .view-product-divider
{
  display: flex;
  justify-content: center;
  align-items: center;
  width: 30%;
}

.divider { margin-top: -20px; }
.view-product-divider { margin-top: -0.5px; }

section { padding: 5rem; }

.add_product,
.update_product 
{
  max-width: 600px;
  background-color: var(--old-burgundy);
  border-radius: 1rem;
  padding: 4rem;
  margin: 0 auto;
  margin-top: 1rem;
}

.add_product .input_fields,
.update_product .input_fields 
{
  padding: 1rem 1.2rem;
  font-size: 1.6rem;
  color: var(--raisin-black);
  border-radius: 0.5rem;
  background-color: var(--magnolia);
  margin: 1.5rem 0;
  width: 100%;
}

.submit_btn,
.edit_btn,
.cancel_btn 
{
  display: block;
  width: 40%;
  text-align: center;
  background-color: var(--don-juan);
  color: var(--magnolia);
  font-size: 1.7rem;
  padding: 1.2rem 3rem;
  border-radius: 0.5rem;
  cursor: pointer;
  margin-top: 2rem;
  margin: 0 auto;
}

.cancel_btn 
{
  background-color: var(--red);
  margin: 2rem auto;
}

.submit_btn:hover { color: var(--color3); }

/* view_products.php */
.display_product table 
{
  width: 100%;
  text-align: center;
}

.display_product table thead th 
{
  padding: 1.5rem;
  font-size: 1.5rem;
  background-color: var(--old-burgundy);
  color: var(--magnolia);
}

td 
{
  padding: 1rem;
  font-size: 1.5rem;
  color: var(--don-juan);
  border: 1px solid var(--magnolia);
  text-transform: uppercase;
}

.empty_text 
{
  background-color: var(--old-burgundy);
  padding: 2.5rem 2rem;
  font-size: 2rem;
  text-align: center;
}

.btns 
{
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.delete_product_btn,
.update_product_btn 
{
  text-align: center;
  color: var(--red);
  font-size: 1.7rem;
  padding: 0 1rem;
  cursor: pointer;
}

.update_product_btn { color: var(--old-burgundy); }

.product_container_box 
{
  text-align: center;
  height: 70%;
}

img 
{
  width: 150px;
  height: 160px;
  margin: 0 auto;
  border-radius: 0.5rem;
}

/* shop_product.php file styles */
.product_container 
{
  display: grid;
  grid-template-columns: repeat(auto-fit, 30rem);
  gap: 2rem;
  justify-content: center;
}

.product_container .edit_form 
{
  text-align: center;
  padding: 2rem;
  box-shadow: var(--raisin-black);
  border: var(--border);
  border-radius: 0.8rem;
  background-color: var(--magnolia);
  box-shadow: 2px 5px 14px #888888;
}

.product_container .edit_form img
{
  width: 180px;
  height: 200px;
  object-fit: cover;
  display: block;
  margin: 0 auto;
  border-radius: 0.5rem;
}

.products .edit_form h3 
{
  margin: 1rem 0;
  font-size: 2rem;
  color: var(--raisin-black);
  text-transform: uppercase;
}

.products .edit_form .price 
{
  font-size: 1.7rem;
  color: var(--don-juan);
  margin: 1rem 0;
}

.cart_btn 
{
  text-align: center;
  padding: 1rem 0;
  font-size: 1.4rem;
}

/* cart.php */
.shopping_cart table 
{
  text-align: center;
  width: 100%;
}

.shopping_cart table thead th 
{
  padding: 1.5rem;
  font-size: 1.6rem;
  color: var(--magnolia);
  background-color: var(--old-burgundy);
}

.table_bottom 
{
  background-color: var(--don-juan);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  margin: 1rem 0 3rem 0;
}

.bottom_btn 
{
  color: var(--raisin-black);
  font-size: 1.45rem;
  background-color: var(--spicy-pink);
  padding: 1rem;
  border-radius: 1rem;
}

.bottom_btn span { color: #581214; }

.remove-btn { color: var(--brandy); }
.remove-btn:hover { color: var(--red); }

.delete_all_btn 
{
  background-color: var(--don-juan);
  color: var(--magnolia);
  align-items: center;
  ;
  font-size: 1.3rem;
  padding: 1.5rem;
  border-radius: 1rem;
}

.delete_all_btn .fa-trash 
{
  color: var(--red);
  margin-right: 0.5rem;
}

.quantity_box 
{
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quantity_box input 
{
  width: 40%;
  padding: 0.5rem;
}

.update_quantity 
{
  color: var(--magnolia);
  padding: 0.5rem;
  background-color: var(--don-juan);
  margin: 1rem 0;
}

.my-swal-title
{
  text-align: center;
  font-size: 2.2rem;
  font-family: "Epilogue", sans-serif;
  color: var(--don-juan);
}

.my-swal-text
{
  text-align: center;
  font-size: 1.8rem;
  font-family: "Epilogue", sans-serif;
  color: var(--raisin-black);
}

.my-confirm-btn, .my-cancel-btn
{
  font-size: 1.2rem;
  font-family: "Epilogue", sans-serif;
}

::-webkit-scrollbar { width: 8px; }

::-webkit-scrollbar-track { background: var(--old-burgundy); }

::-webkit-scrollbar-thumb 
{ 
  background: var(--spicy-pink);
  border-radius: 1rem; 
}

::-webkit-scrollbar-thumb:hover { background: var(--spicy-pink); }

/* media queries */
@media (max-width: 1200px) 
{
  .shopping_cart { overflow-x: scroll; }

  .shopping_cart table { width: 120rem; }

  .shopping_cart .heading { text-align: left; }

  .shopping_cart .checkout-btn { text-align: left; }
}

@media (max-width: 991px) 
{
  html { font-size: 55%; }
}

@media (max-width: 768px) 
{
  #menu-btn 
  {
    display: inline-block;
    transition: 0.2s linear;
  }

  #menu-btn.fa-times { transform: rotate(180deg); }

  .header .header_body .navbar 
  {
    position: absolute;
    top: 99%;
    left: 0;
    right: 0;
    background-color: var(--blue);
    clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
    transition: 0.2s linear;
  }

  .header .header_body .navbar.active 
  {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
    background-color: var(--don-juan);
  }

  .header .header_body .navbar a 
  {
    margin: 2rem;
    display: block;
    text-align: center;
    font-size: 2.5rem;
  }

  .display_product { overflow-x: scroll; }

  .display_product table { width: 90rem; }

  .table_bottom 
  {
    display: block;
    text-align: center;
  }

  .bottom_btn 
  {
    color: var(--raisin-black);
    font-size: 1.3rem;
    background-color: var(--spicy-pink);
    padding: 1rem;
    margin: 2rem 0;
  }
}

@media (max-width: 450px) 
{
  html { font-size: 50%; }

  .heading { font-size: 3rem; }

  .products .product_container { grid-template-columns: 1fr; }
}