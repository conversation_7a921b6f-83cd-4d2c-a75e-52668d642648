<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// Database configuration
$host = 'localhost';
$dbname = 'group4_db';
$username = 'root'; // Change this to your database username
$password = '';     // Change this to your database password

try {
    // Create PDO connection
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? 'get_random_products';
    $limit = $input['limit'] ?? 5;
    
    if ($action === 'get_random_products') {
        // Fetch random products from the products table
        $stmt = $pdo->prepare("SELECT name, description FROM products ORDER BY RAND() LIMIT :limit");
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Add IDs for frontend use
        foreach ($products as $index => &$product) {
            $product['id'] = $index + 1;
        }
        
        echo json_encode([
            'success' => true,
            'data' => $products,
            'message' => 'Products loaded successfully from database'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'Invalid action'
        ]);
    }
    
} catch (PDOException $e) {
    // Database connection failed, return error
    echo json_encode([
        'success' => false,
        'error' => 'Database connection failed: ' . $e->getMessage(),
        'fallback' => true
    ]);
} catch (Exception $e) {
    // General error
    echo json_encode([
        'success' => false,
        'error' => 'An error occurred: ' . $e->getMessage()
    ]);
}
?>