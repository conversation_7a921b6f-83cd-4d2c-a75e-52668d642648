<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// Database configuration
$host = 'localhost';
$dbname = 'group4_db';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Database connection failed: ' . $e->getMessage()]);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);
$productIds = isset($input['productIds']) ? $input['productIds'] : [];

if (empty($productIds)) {
    echo json_encode(['success' => true, 'products' => []]);
    exit;
}

// Create placeholders for IN clause
$placeholders = str_repeat('?,', count($productIds) - 1) . '?';

// Get saved products
$query = "SELECT * FROM products WHERE id IN ($placeholders)";
$stmt = $pdo->prepare($query);
$stmt->execute($productIds);

$products = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo json_encode([
    'success' => true,
    'products' => $products
]);
?>