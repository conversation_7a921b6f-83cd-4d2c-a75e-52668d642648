@import url("https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700;800;900&display=swap");
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: #e6ccb2;
    border-radius: 6px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #582f0e 0%, #8b4513 100%);
    border-radius: 6px;
    border: 2px solid #e6ccb2;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #8b4513 0%, #582f0e 100%);
}

body {
    font-family: 'Poppins', sans-serif;
    background: white;
    overflow-x: hidden;
}

.container {
    position: relative;
    width: 100vw;
    min-height: 100vh;
    background: white;
}

.content-wrapper {
    width: 100%;
    position: relative;
    padding: 0 20px;
    z-index: 5;
}

/* Background Images */
.bg-image {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    object-fit: cover;
    z-index: 1;
}

/* Header */
.header {
    position: relative;
    z-index: 10;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 46px 80px 0;
    width: 100%;
}

.nav-left .logo {
    width: 53px;
    height: 49px;
    border-radius: 8px;
}

.nav-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.nav-links {
    display: flex;
    align-items: center;
    list-style: none;
    gap: 0;
}

.nav-link {
    font-family: 'Poppins', sans-serif;
    color: #582f0e;
    font-size: 24px;
    text-decoration: none;
    font-weight: 600;
    letter-spacing: 0.48px;
    transition: all 0.3s ease;
}

.nav-link:hover {
    font-weight: 700;
    text-decoration: underline;
    transform: translateY(-2px);
}

.nav-link.active {
    font-weight: 700;
    text-decoration: underline;
}

.nav-separator {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: #582f0e;
    font-size: 24px;
    margin: 0 8px;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.nav-icon {
    width: 28px;
    height: 24px;
    color: #582f0e;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.nav-icon:hover {
    transform: scale(1.1);
}

.clock {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: #582f0e;
    font-size: 24px;
    letter-spacing: 0.24px;
    min-width: 120px;
    text-align: center;
}

/* Page Title */
.page-title {
    font-family: 'Playfair Display', serif;
    font-weight: 800;
    font-size: 100px;
    color: #e6ccb2;
    -webkit-text-stroke: 2px #000000;
    text-align: center;
    margin: 9px 0 61px;
    letter-spacing: 0;
}

/* Container */
.container1 {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    margin-top: 80px; /* Account for fixed nav */
    z-index: 20;
}

/* Header */
.cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 192, 203, 0.3);
    box-shadow: 0 4px 16px rgba(255, 192, 203, 0.1);
}

.cart-header h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #8b4513; /* Darker brown for contrast */
}

.back-to-shop-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 192, 203, 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: #8b4513;
    border: 1px solid rgba(255, 192, 203, 0.4);
    padding: 12px 20px;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    box-shadow: 0 4px 16px rgba(255, 192, 203, 0.2);
}

.back-to-shop-btn:hover {
    background: rgba(255, 182, 193, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 192, 203, 0.3);
    border: 1px solid rgba(255, 192, 203, 0.5);
}

/* Cart content layout */
.cart-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    align-items: start;
    z-index: 10;
}

/* Center cart content when only empty cart is visible */
.cart-content:has(.cart-items-section[style*="display: none"]) {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
}

/* Fallback for browsers that don't support :has() */
.cart-content.empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
}

/* Cart Items Section */
.cart-items-section {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    z-index: 10;
}

.cart-items {
    padding: 20px;
    z-index: 10;
}

.cart-item {
    display: grid;
    grid-template-columns: 100px 1fr auto auto auto;
    gap: 20px;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    z-index: 10;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item:hover {
    background: rgba(255, 255, 255, 0.4);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 12px;
    margin: 0 -10px;
    padding: 20px 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

.item-image {
    width: 100px;
    height: 100px;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid #ffc0cb;
    z-index: 10;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.item-image:hover img {
    transform: scale(1.05);
}

.item-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.item-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.item-description {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
}

.item-price {
    font-size: 1rem;
    font-weight: 500;
    color: #8b4513;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    background-color: #f8f8f8;
    border-radius: 8px;
    padding: 8px;
    border: 1px solid #e0e0e0;
}

.quantity-btn {
    width: 32px;
    height: 32px;
    border: 1px solid rgba(255, 192, 203, 0.4);
    background: rgba(255, 192, 203, 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: #8b4513;
    border-radius: 8px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(255, 192, 203, 0.2);
}

.quantity-btn:hover {
    background: rgba(255, 182, 193, 0.4);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(255, 192, 203, 0.3);
    border: 1px solid rgba(255, 192, 203, 0.5);
}

.quantity-btn:disabled {
    background-color: #e0e0e0;
    color: #999;
    cursor: not-allowed;
    transform: none;
}

.quantity-display {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    min-width: 30px;
    text-align: center;
}

.item-total {
    font-size: 1.2rem;
    font-weight: 600;
    color: #8b4513;
    min-width: 80px;
    text-align: right;
}

.remove-btn {
    width: 36px;
    height: 36px;
    border: 1px solid rgba(255, 107, 107, 0.4);
    background: rgba(255, 107, 107, 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: white;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.2);
}

.remove-btn:hover {
    background: rgba(255, 82, 82, 0.4);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
    border: 1px solid rgba(255, 107, 107, 0.5);
}

/* Empty Cart */
.empty-cart {
    text-align: center;
    padding: 60px 40px;
    color: #666;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin: 20px;
    max-width: 500px;
    width: 100%;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* When cart is in empty state, make the empty cart panel more prominent */
.cart-content.empty-state .empty-cart {
    padding: 80px 60px;
    max-width: 600px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.empty-cart-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-cart h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #8b4513;
}

.empty-cart p {
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.continue-shopping-btn {
    background: rgba(255, 192, 203, 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: #8b4513;
    border: 1px solid rgba(255, 192, 203, 0.4);
    padding: 12px 24px;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(255, 192, 203, 0.2);
}

.continue-shopping-btn:hover {
    background: rgba(255, 182, 193, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 192, 203, 0.3);
    border: 1px solid rgba(255, 192, 203, 0.5);
}

/* Cart Summary Section */
.cart-summary-section {
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(255, 192, 203, 0.4);
    box-shadow: 0 8px 32px rgba(255, 192, 203, 0.2);
    height: fit-content;
    position: sticky;
    top: 100px;
    z-index: 20;
}

.cart-summary {
    padding: 30px;
}

.cart-summary h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #8b4513;
    margin-bottom: 25px;
    text-align: center;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 1rem;
    color: #333;
}

.summary-row span:last-child {
    font-weight: 500;
    color: #8b4513;
}

.summary-divider {
    height: 2px;
    background-color: #ffc0cb;
    margin: 20px 0;
    border-radius: 1px;
}

.total-row {
    font-size: 1.3rem;
    font-weight: 600;
    color: #8b4513;
    margin-bottom: 25px;
    padding-top: 10px;
}

.checkout-btn {
    width: 100%;
    background: linear-gradient(135deg, rgba(139, 69, 19, 0.9), rgba(160, 82, 45, 0.9));
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 16px 24px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(139, 69, 19, 0.3);
}

.checkout-btn:hover {
    background: linear-gradient(135deg, rgba(160, 82, 45, 0.95), rgba(139, 69, 19, 0.95));
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(139, 69, 19, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.checkout-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Item removal animation */
.cart-item.removing {
    animation: slideOut 0.3s ease-out forwards;
}

/* Footer styling removed - back button moved to nav-center */


@keyframes slideOut {
    0% {
        opacity: 1;
        transform: translateX(0);
        max-height: 140px;
    }
    50% {
        opacity: 0;
        transform: translateX(-20px);
    }
    100% {
        opacity: 0;
        transform: translateX(-100%);
        max-height: 0;
        padding: 0;
        margin: 0;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .cart-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    /* Ensure empty state centering works on mobile */
    .cart-content.empty-state {
        min-height: 300px;
        padding: 20px;
    }

    .cart-content.empty-state .empty-cart {
        padding: 60px 30px;
        max-width: 100%;
        margin: 0;
    }
    
    .cart-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .cart-header h1 {
        font-size: 2rem;
    }
    
    .cart-item {
        grid-template-columns: 80px 1fr;
        gap: 15px;
        grid-template-areas: 
            "image details"
            "image controls"
            "total total";
    }
    
    .item-image {
        grid-area: image;
        width: 80px;
        height: 80px;
    }
    
    .item-details {
        grid-area: details;
    }
    
    .quantity-controls {
        grid-area: controls;
        justify-self: start;
        margin-top: 10px;
    }
    
    .item-total {
        grid-area: total;
        text-align: left;
        margin-top: 10px;
        font-size: 1.1rem;
    }
    
    .remove-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        font-size: 1.2rem;
    }
    
    .cart-item {
        position: relative;
        padding: 15px;
        margin: 10px 0;
        background-color: #fafafa;
        border-radius: 8px;
        border-bottom: none;
    }
    
    .container1 {
        padding: 15px;
    }
    
    .cart-summary {
        padding: 20px;
    }
    
    .nav-content {
        padding: 0 15px;
    }
    
    .nav-left h3 {
        font-size: 1.3rem;
    }
    
    .real-time-clock {
        font-size: 0.8rem;
        padding: 6px 12px;
    }

    .nav-center {
        position: static;
        transform: none;
    }

    .nav-links {
        flex-wrap: wrap;
        justify-content: center;
    }

    .nav-link {
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    .cart-header h1 {
        font-size: 1.8rem;
    }
    
    .back-to-shop-btn {
        padding: 10px 16px;
        font-size: 0.9rem;
    }
    
    .cart-summary h2 {
        font-size: 1.3rem;
    }
    
    .item-name {
        font-size: 1.1rem;
    }
    
    .item-description {
        font-size: 0.8rem;
    }

    .nav-link {
        font-size: 18px;
    }
}