<?php
include 'db_connection.php';

$username = $_POST['username'];
$password = $_POST['password'];
$passcode = $_POST['passcode'];

// Example passcode
$valid_passcode = "admin2024";

if ($passcode !== $valid_passcode) {
    echo "<script>alert('Invalid passcode!'); window.location.href='login_admin.php';</script>";
    exit;
}

$hashed_password = password_hash($password, PASSWORD_DEFAULT);

$sql = "INSERT INTO admins (username, password) VALUES (?, ?)";
$stmt = $conn->prepare($sql);

if (!$stmt) {
    die("SQL error: " . $conn->error);
}

$stmt->bind_param("ss", $username, $hashed_password);

if ($stmt->execute()) {
    echo "<script>alert('Admin registered successfully. Please log in.'); window.location.href='login_admin.php';</script>";
} else {
    echo "<script>alert('Username already taken.'); window.location.href='login_admin.php';</script>";
}

$stmt->close();
$conn->close();
?>
