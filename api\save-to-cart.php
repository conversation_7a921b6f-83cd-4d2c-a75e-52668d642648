<?php
// Enable error logging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Log all requests for debugging
file_put_contents('cart_debug.log', date('Y-m-d H:i:s') . " - Request received\n", FILE_APPEND);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Database connection
$host = 'localhost';
$dbname = 'group4_db';
$username = 'root';
$password = '';

try {
    // Get JSON data
    $input = file_get_contents("php://input");
    file_put_contents('cart_debug.log', date('Y-m-d H:i:s') . " - Raw input: $input\n", FILE_APPEND);
    
    $data = json_decode($input, true);
    
    if (!$data || !isset($data['product_id']) || !isset($data['quantity'])) {
        throw new Exception('Invalid data received');
    }
    
    // Connect to database
    $conn = new mysqli($host, $username, $password, $dbname);
    
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    // Prepare data for insertion
    $productId = $conn->real_escape_string($data['product_id']);
    $quantity = (int)$data['quantity'];
    
    // Get product details from products table
    $productQuery = "SELECT name, price, image FROM products WHERE id = '$productId'";
    $productResult = $conn->query($productQuery);

    if ($productResult->num_rows == 0) {
        throw new Exception("Product with ID $productId not found");
    }

    $productRow = $productResult->fetch_assoc();
    $productName = $conn->real_escape_string($productRow['name']);
    $productPrice = (float)$productRow['price'];
    $productImage = $conn->real_escape_string($productRow['image']);
    
    // Check if product exists in cart
    $checkSql = "SELECT * FROM cart WHERE product_id = '$productId'";
    $result = $conn->query($checkSql);
    
    if ($result->num_rows > 0) {
        // Update existing item
        $sql = "UPDATE cart SET quantity = quantity + $quantity WHERE product_id = '$productId'";
    } else {
        // Insert new item with product details
        $sql = "INSERT INTO cart (product_id, product_name, product_image, product_price, quantity) VALUES ('$productId', '$productName', '$productImage', $productPrice, $quantity)";
    }
    
    // Execute query
    if ($conn->query($sql) === TRUE) {
        $response = [
            'success' => true,
            'message' => 'Item added to cart successfully'
        ];
    } else {
        throw new Exception("Error: " . $sql . "<br>" . $conn->error);
    }
    
    $conn->close();
    
    echo json_encode($response);
    
} catch (Exception $e) {
    file_put_contents('cart_debug.log', date('Y-m-d H:i:s') . " - Error: " . $e->getMessage() . "\n", FILE_APPEND);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
?>





