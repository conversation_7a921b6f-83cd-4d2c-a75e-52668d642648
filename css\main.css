* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #654321;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 15px 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(139, 69, 19, 0.1);
    border-bottom: 1px solid rgba(210, 180, 140, 0.2);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 12px;
    font-family: 'Playfair Display', serif;
}

.brand-icon {
    font-size: 2rem;
    color: #8b4513;
}

.brand-name {
    font-size: 1.8rem;
    font-weight: 700;
    color: #8b4513;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.welcome-text {
    font-weight: 500;
    color: #654321;
}

.logout-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.logout-btn:hover {
    background: linear-gradient(135deg, #654321 0%, #8b4513 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: linear-gradient(135deg, #f5e6d3 0%, #e8d5b0 50%, #d4b896 100%);
    padding-top: 80px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(139, 69, 19, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(210, 180, 140, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(160, 82, 45, 0.05) 0%, transparent 50%);
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.float-item {
    position: absolute;
    font-size: 2rem;
    color: rgba(139, 69, 19, 0.1);
    animation: float 6s ease-in-out infinite;
}

.float-1 { top: 10%; left: 10%; animation-delay: 0s; }
.float-2 { top: 20%; right: 15%; animation-delay: 1s; }
.float-3 { top: 60%; left: 8%; animation-delay: 2s; }
.float-4 { bottom: 20%; right: 10%; animation-delay: 3s; }
.float-5 { top: 40%; left: 5%; animation-delay: 4s; }
.float-6 { bottom: 10%; left: 20%; animation-delay: 5s; }

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.hero-text {
    margin-bottom: 50px;
}

.hero-title {
    font-family: 'Playfair Display', serif;
    font-size: 4rem;
    font-weight: 700;
    color: #8b4513;
    margin-bottom: 20px;
    line-height: 1.2;
}

.brand-highlight {
    background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: #654321;
    max-width: 600px;
    margin: 0 auto 40px;
    font-weight: 400;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 60px;
    margin-bottom: 40px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #8b4513;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 1rem;
    color: #a67c52;
    font-weight: 500;
}

.hero-cta {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.cta-button {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 18px 35px;
    border: none;
    border-radius: 16px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    text-decoration: none;
}

.cta-button.primary {
    background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
    color: white;
    box-shadow: 0 12px 24px rgba(139, 69, 19, 0.3);
}

.cta-button.secondary {
    background: rgba(255, 255, 255, 0.9);
    color: #8b4513;
    border: 2px solid #8b4513;
    box-shadow: 0 8px 16px rgba(139, 69, 19, 0.1);
}

.cta-button.large {
    padding: 20px 40px;
    font-size: 1.2rem;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button.primary:hover {
    background: linear-gradient(135deg, #654321 0%, #8b4513 100%);
    transform: translateY(-3px);
    box-shadow: 0 16px 32px rgba(139, 69, 19, 0.4);
}

.cta-button.secondary:hover {
    background: #8b4513;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 12px 24px rgba(139, 69, 19, 0.3);
}

.button-icon {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.cta-button:hover .button-icon {
    transform: translateX(5px);
}

/* Section Titles */
.section-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.8rem;
    font-weight: 700;
    color: #8b4513;
    text-align: center;
    margin-bottom: 60px;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
    border-radius: 2px;
}

/* Features Section */
.features {
    padding: 100px 0;
    background: rgba(255, 255, 255, 0.8);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;
}

.feature-card {
    background: rgba(255, 255, 255, 0.9);
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(139, 69, 19, 0.1);
    border: 1px solid rgba(210, 180, 140, 0.2);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(139, 69, 19, 0.2);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    font-size: 2rem;
    color: white;
    transition: all 0.4s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
}

.feature-title {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: #8b4513;
    margin-bottom: 15px;
}

.feature-description {
    color: #654321;
    line-height: 1.6;
    font-size: 1rem;
}

/* Categories Section */
.categories {
    padding: 100px 0;
    background: linear-gradient(135deg, #f5e6d3 0%, #e8d5b0 100%);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
}

.category-card {
    background: rgba(255, 255, 255, 0.9);
    padding: 30px 20px;
    border-radius: 16px;
    text-align: center;
    cursor: pointer;
    transition: all 0.4s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.category-card:hover::before {
    opacity: 0.05;
}

.category-card:hover {
    transform: translateY(-8px);
    border-color: #8b4513;
    box-shadow: 0 12px 32px rgba(139, 69, 19, 0.2);
}

.category-image {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 1.8rem;
    color: white;
    transition: all 0.4s ease;
    position: relative;
    z-index: 2;
}

.category-card:hover .category-image {
    transform: scale(1.1) rotate(-5deg);
}

.category-title {
    font-family: 'Playfair Display', serif;
    font-size: 1.3rem;
    font-weight: 600;
    color: #8b4513;
    margin-bottom: 8px;
    position: relative;
    z-index: 2;
}

.category-count {
    color: #a67c52;
    font-size: 0.9rem;
    font-weight: 500;
    position: relative;
    z-index: 2;
}

/* Final CTA Section */
.final-cta {
    padding: 100px 0;
    background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.final-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="grain" patternUnits="userSpaceOnUse" width="60" height="60"><circle cx="30" cy="30" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="60" height="60" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.cta-content {
    position: relative;
    z-index: 2;
    max-width: 700px;
    margin: 0 auto;
}

.cta-title {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cta-description {
    font-size: 1.2rem;
    margin-bottom: 40px;
    opacity: 0.9;
    line-height: 1.6;
}

.final-cta .cta-button {
    background: white;
    color: #8b4513;
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
}

.final-cta .cta-button:hover {
    background: #f8f9fa;
    transform: translateY(-3px);
    box-shadow: 0 16px 32px rgba(0, 0, 0, 0.3);
}

/* Footer */
.footer {
    background: #654321;
    color: rgba(255, 255, 255, 0.9);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 60px;
    margin-bottom: 40px;
}

.footer-brand {
    max-width: 350px;
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-family: 'Playfair Display', serif;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.brand-logo i {
    font-size: 2rem;
    color: #d2b48c;
}

.brand-description {
    line-height: 1.6;
    opacity: 0.8;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
}

.link-group h4 {
    font-family: 'Playfair Display', serif;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #d2b48c;
}

.link-group ul {
    list-style: none;
}

.link-group ul li {
    margin-bottom: 10px;
}

.link-group ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
}

.link-group ul li a:hover {
    color: #d2b48c;
    padding-left: 5px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    text-align: center;
    opacity: 0.7;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 15px;
    }
    
    .brand-name {
        font-size: 1.5rem;
    }
    
    .user-info {
        flex-direction: column;
        gap: 10px;
        align-items: flex-end;
    }
    
    .welcome-text {
        font-size: 0.9rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 20px;
    }
    
    .hero-cta {
        flex-direction: column;
        align-items: center;
    }
    
    .cta-button {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
    
    .section-title {
        font-size: 2.2rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .cta-title {
        font-size: 2.2rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .footer-links {
        grid-template-columns: 1fr;
        gap: 30px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .hero {
        padding-top: 70px;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
    }
    
    .cta-title {
        font-size: 1.8rem;
    }
    
    .cta-description {
        font-size: 1rem;
    }
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Add animation classes */
.animate-slide-up {
    animation: slideInUp 0.6s ease-out forwards;
}

.animate-fade-scale {
    animation: fadeInScale 0.8s ease-out forwards;
}