<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "group4_db";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "Connected successfully<br>";

// Hard-coded values for testing
$firstName = "John";
$lastName = "Doe";
$username = "johndoe" . rand(1000, 9999);
$email = "john" . rand(1000, 9999) . "@example.com";
$phone = "1234567890";
$password = password_hash("password123", PASSWORD_DEFAULT);

// Simple insert query
$sql = "INSERT INTO users (first_name, last_name, username, email, phone, password) 
        VALUES ('$firstName', '$lastName', '$username', '$email', '$phone', '$password')";

if ($conn->query($sql) === TRUE) {
    echo "New record created successfully with ID: " . $conn->insert_id;
} else {
    echo "Error: " . $sql . "<br>" . $conn->error;
}

$conn->close();
?>