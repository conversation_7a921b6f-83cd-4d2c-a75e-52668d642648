<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Log request method and content type
file_put_contents('register_log.txt', date('Y-m-d H:i:s') . " - Request method: " . $_SERVER['REQUEST_METHOD'] . "\n", FILE_APPEND);
file_put_contents('register_log.txt', date('Y-m-d H:i:s') . " - Content-Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'not set') . "\n", FILE_APPEND);

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "group4_db";

try {
    // Create connection
    $conn = new mysqli($servername, $username, $password, $dbname);

    // Check connection
    if ($conn->connect_error) {
        file_put_contents('register_log.txt', date('Y-m-d H:i:s') . " - Connection failed: " . $conn->connect_error . "\n", FILE_APPEND);
        throw new Exception("Connection failed: " . $conn->connect_error);
    }

    file_put_contents('register_log.txt', date('Y-m-d H:i:s') . " - Connection successful\n", FILE_APPEND);

    // Get data from either POST or JSON input
    if (isset($_POST['username'])) {
        // Form data
        file_put_contents('register_log.txt', date('Y-m-d H:i:s') . " - Using POST data\n", FILE_APPEND);
        file_put_contents('register_log.txt', date('Y-m-d H:i:s') . " - POST data: " . print_r($_POST, true) . "\n", FILE_APPEND);
        
        $data = $_POST;
    } else {
        // JSON data
        $rawInput = file_get_contents('php://input');
        file_put_contents('register_log.txt', date('Y-m-d H:i:s') . " - Using JSON data\n", FILE_APPEND);
        file_put_contents('register_log.txt', date('Y-m-d H:i:s') . " - Raw input: " . $rawInput . "\n", FILE_APPEND);
        
        $data = json_decode($rawInput, true);
        
        if ($data === null) {
            file_put_contents('register_log.txt', date('Y-m-d H:i:s') . " - JSON decode error: " . json_last_error_msg() . "\n", FILE_APPEND);
            throw new Exception("Invalid JSON: " . json_last_error_msg());
        }
    }
    
    // Extract data
    $firstName = $data['firstName'] ?? '';
    $lastName = $data['lastName'] ?? '';
    $username = $data['username'] ?? '';
    $email = $data['email'] ?? '';
    $phone = $data['phone'] ?? '';
    $password = $data['password'] ?? '';
    
    // Validate required fields
    if (empty($username) || empty($email) || empty($password)) {
        throw new Exception("Username, email, and password are required");
    }
    
    // Check if username or email already exists
    $checkSql = "SELECT * FROM users WHERE username = ? OR email = ?";
    $checkStmt = $conn->prepare($checkSql);
    
    if (!$checkStmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }
    
    $checkStmt->bind_param("ss", $username, $email);
    $checkStmt->execute();
    $result = $checkStmt->get_result();
    
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        if ($user['email'] == $email) {
            throw new Exception("Email already registered");
        } else {
            throw new Exception("Username already taken");
        }
    }
    
    // Hash password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    // Insert new user
    $sql = "INSERT INTO users (first_name, last_name, username, email, phone, password) 
            VALUES (?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }
    
    $stmt->bind_param("ssssss", $firstName, $lastName, $username, $email, $phone, $hashedPassword);
    
    if ($stmt->execute()) {
        file_put_contents('register_log.txt', date('Y-m-d H:i:s') . " - User registered successfully with ID: " . $conn->insert_id . "\n", FILE_APPEND);
        echo json_encode([
            'success' => true, 
            'message' => 'Registration successful!',
            'userId' => $conn->insert_id
        ]);
    } else {
        throw new Exception("Execute failed: " . $stmt->error);
    }
    
    $stmt->close();
    $checkStmt->close();
    
} catch (Exception $e) {
    file_put_contents('register_log.txt', date('Y-m-d H:i:s') . " - Error: " . $e->getMessage() . "\n", FILE_APPEND);
    echo json_encode([
        'success' => false, 
        'message' => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>



