* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #f5e6d3 0%, #e8d5b0 50%, #d4b896 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
}

body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(139, 69, 19, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(210, 180, 140, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.container {
    width: 100%;
    max-width: 450px;
    position: relative;
    z-index: 1;
}

.login-wrapper {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 24px;
    box-shadow: 
        0 20px 40px rgba(139, 69, 19, 0.1),
        0 8px 16px rgba(139, 69, 19, 0.05);
    overflow: hidden;
    border: 1px solid rgba(210, 180, 140, 0.2);
}

/* Logo Section */
.logo-section {
    background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
    padding: 40px 20px;
    text-align: center;
    color: white;
    position: relative;
}

.logo-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="grain" patternUnits="userSpaceOnUse" width="60" height="60"><circle cx="30" cy="30" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="60" height="60" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.logo {
    font-size: 3rem;
    margin-bottom: 15px;
    position: relative;
    z-index: 1;
}

.brand-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    position: relative;
    z-index: 1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.brand-subtitle {
    font-size: 0.95rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
    font-weight: 300;
}

/* Auth Card */
.auth-card {
    padding: 40px 30px;
}

/* Toggle Buttons */
.auth-toggle {
    display: flex;
    background: #f5e6d3;
    border-radius: 16px;
    padding: 6px;
    margin-bottom: 30px;
    position: relative;
}

.toggle-btn {
    flex: 1;
    padding: 14px 20px;
    border: none;
    background: transparent;
    border-radius: 12px;
    font-weight: 500;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #8b4513;
    position: relative;
    z-index: 2;
}

.toggle-btn.active {
    background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
    transform: translateY(-1px);
}

.toggle-btn:hover:not(.active) {
    color: #654321;
    background: rgba(139, 69, 19, 0.1);
}

/* Forms */
.auth-form {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.auth-form.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 0;
}

.form-group {
    margin-bottom: 20px;
}

.form-group.half {
    flex: 1;
    margin-bottom: 20px;
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 16px;
    color: #8b4513;
    font-size: 1.1rem;
    z-index: 2;
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"] {
    width: 100%;
    padding: 16px 20px 16px 50px;
    border: 2px solid #e8d5b0;
    border-radius: 14px;
    background: rgba(245, 230, 211, 0.3);
    font-size: 1rem;
    font-weight: 400;
    color: #654321;
    transition: all 0.3s ease;
    outline: none;
}

input:focus {
    border-color: #8b4513;
    background: rgba(245, 230, 211, 0.5);
    box-shadow: 0 0 0 4px rgba(139, 69, 19, 0.1);
    transform: translateY(-2px);
}

input::placeholder {
    color: #a67c52;
    font-weight: 300;
}

.password-toggle {
    position: absolute;
    right: 16px;
    background: none;
    border: none;
    color: #8b4513;
    cursor: pointer;
    font-size: 1.1rem;
    padding: 4px;
    border-radius: 6px;
    transition: all 0.2s ease;
    z-index: 2;
}

.password-toggle:hover {
    background: rgba(139, 69, 19, 0.1);
    transform: scale(1.1);
}

.error-message {
    display: block;
    color: #d32f2f;
    font-size: 0.8rem;
    margin-top: 6px;
    margin-left: 4px;
    opacity: 0;
    transform: translateY(-5px);
    transition: all 0.3s ease;
    min-height: 16px;
}

.error-message.show {
    opacity: 1;
    transform: translateY(0);
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 10px;
}

.checkbox-container {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: #654321;
    cursor: pointer;
    user-select: none;
}

.checkbox-container input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #e8d5b0;
    border-radius: 4px;
    margin-right: 10px;
    position: relative;
    transition: all 0.3s ease;
    background: rgba(245, 230, 211, 0.3);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
    border-color: #8b4513;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.forgot-password,
.terms-link {
    color: #8b4513;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.forgot-password:hover,
.terms-link:hover {
    color: #654321;
    text-decoration: underline;
}

/* Submit Button */
.submit-btn {
    width: 100%;
    padding: 16px 20px;
    background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
    color: white;
    border: none;
    border-radius: 14px;
    font-size: 1.05rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 16px rgba(139, 69, 19, 0.2);
    position: relative;
    overflow: hidden;
}

.submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.submit-btn:hover {
    background: linear-gradient(135deg, #654321 0%, #8b4513 100%);
    box-shadow: 0 12px 20px rgba(139, 69, 19, 0.3);
    transform: translateY(-2px);
}

.submit-btn:hover::before {
    left: 100%;
}

.submit-btn:active {
    transform: translateY(0);
    box-shadow: 0 4px 8px rgba(139, 69, 19, 0.2);
}

/* Divider */
.divider {
    display: flex;
    align-items: center;
    margin: 30px 0;
    color: #a67c52;
    font-size: 0.9rem;
}

.divider::before,
.divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: linear-gradient(to right, transparent, #e8d5b0, transparent);
}

.divider span {
    padding: 0 20px;
    background: rgba(255, 255, 255, 0.95);
    font-weight: 500;
}

/* Google Button */
.google-btn {
    width: 100%;
    padding: 16px 20px;
    background: white;
    color: #654321;
    border: 2px solid #e8d5b0;
    border-radius: 14px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    position: relative;
    overflow: hidden;
}

.google-btn i {
    font-size: 1.2rem;
    color: #db4437;
}

.google-btn:hover {
    background: #f8f9fa;
    border-color: #8b4513;
    box-shadow: 0 8px 16px rgba(139, 69, 19, 0.1);
    transform: translateY(-2px);
}

.google-btn:active {
    transform: translateY(0);
}

/* Success Message */
.success-message {
    display: none;
    background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
    color: white;
    padding: 16px 20px;
    border-radius: 14px;
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 500;
    box-shadow: 0 8px 16px rgba(76, 175, 80, 0.2);
    animation: slideIn 0.5s ease-out;
}

.success-message.show {
    display: flex;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.success-message i {
    font-size: 1.3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        max-width: 100%;
        margin: 10px;
    }
    
    .auth-card {
        padding: 30px 20px;
    }
    
    .brand-title {
        font-size: 2rem;
    }
    
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .form-group.half {
        margin-bottom: 20px;
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
}

@media (max-width: 480px) {
    body {
        padding: 10px;
    }
    
    .logo-section {
        padding: 30px 15px;
    }
    
    .brand-title {
        font-size: 1.8rem;
    }
    
    .auth-card {
        padding: 25px 15px;
    }
    
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="tel"] {
        padding: 14px 18px 14px 45px;
    }
    
    .input-icon {
        left: 14px;
    }
    
    .password-toggle {
        right: 14px;
    }
}