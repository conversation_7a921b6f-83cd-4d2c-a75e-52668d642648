// Global variables to store user data
let userData = {
  name: '(NAME)',
  email: '<EMAIL>',
  profileImage: null,
  quizResult: null
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
  loadUserData();
  loadQuizResult();
  updateClock();
  setInterval(updateClock, 1000);

  // Add click handlers for menu items
  setupMenuHandlers();
});

// Load user data from database
async function loadUserData() {
  try {
    // Get current user from localStorage
    const currentUser = localStorage.getItem('currentSnackAtlasUser');

    if (!currentUser) {
      // Redirect to login if not logged in
      window.location.href = 'login.html';
      return;
    }

    const user = JSON.parse(currentUser);
    const userId = user.id;

    // Fetch fresh user data from database
    const response = await fetch(`api/get_user_profile.php?user_id=${userId}`);
    const result = await response.json();

    if (result.success) {
      // Update userData with database values
      userData.name = result.user.full_name || result.user.first_name || '(NAME)';
      userData.email = result.user.email || '<EMAIL>';
      userData.firstName = result.user.first_name;
      userData.lastName = result.user.last_name;
      userData.username = result.user.username;
      userData.phone = result.user.phone;

      // Load any saved profile customizations from localStorage
      const savedData = localStorage.getItem('profileData');
      if (savedData) {
        const localData = JSON.parse(savedData);
        // Only override profile image from localStorage, keep database data for name/email
        userData.profileImage = localData.profileImage;
      }

      // Update the display
      updateProfileDisplay();
    } else {
      console.error('Failed to load user data:', result.message);
      // Fall back to localStorage data
      const savedData = localStorage.getItem('profileData');
      if (savedData) {
        userData = { ...userData, ...JSON.parse(savedData) };
      }
      updateProfileDisplay();
    }
  } catch (error) {
    console.error('Error loading user data:', error);
    // Fall back to localStorage data
    const savedData = localStorage.getItem('profileData');
    if (savedData) {
      userData = { ...userData, ...JSON.parse(savedData) };
    }
    updateProfileDisplay();
  }
}

// Load quiz result from localStorage
function loadQuizResult() {
  const quizData = localStorage.getItem('quizResult');
  if (quizData) {
    userData.quizResult = JSON.parse(quizData);
  }
}

// Save user data to localStorage
function saveUserData() {
  localStorage.setItem('profileData', JSON.stringify(userData));
}

// Real-time clock functionality
function updateClock() {
  const now = new Date();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  const ampm = hours >= 12 ? 'PM' : 'AM';
  const displayHours = hours % 12 || 12;
  const displayMinutes = minutes.toString().padStart(2, '0');
  
  const timeString = `${displayHours}:${displayMinutes} ${ampm}`;
  const clockElement = document.getElementById('clock');
  if (clockElement) {
    clockElement.textContent = timeString;
  }
}

// Update profile display with current data
function updateProfileDisplay() {
  const displayName = document.getElementById('displayName');
  const profileEmail = document.querySelector('.profile-email');
  const profileImage = document.getElementById('profileImage');
  const avatarPlaceholder = document.getElementById('avatarPlaceholder');

  // Update name
  if (displayName) {
    displayName.textContent = userData.name;
  }

  // Update email
  if (profileEmail) {
    profileEmail.textContent = userData.email;
  }

  // Update profile image
  if (userData.profileImage && profileImage && avatarPlaceholder) {
    profileImage.src = userData.profileImage;
    profileImage.style.display = 'block';
    avatarPlaceholder.style.display = 'none';
  } else if (profileImage && avatarPlaceholder) {
    profileImage.style.display = 'none';
    avatarPlaceholder.style.display = 'flex';
  }

  // Display quiz result if available
  displayQuizResult();
}

// Display quiz result in profile
function displayQuizResult() {
  if (!userData.quizResult) return;
  
  const menuList = document.querySelector('.menu-list');
  if (!menuList) return;
  
  // Check if quiz result item already exists
  let quizResultItem = document.getElementById('quizResultItem');
  
  if (!quizResultItem) {
    // Create quiz result menu item
    quizResultItem = document.createElement('li');
    quizResultItem.id = 'quizResultItem';
    quizResultItem.className = 'menu-item quiz-result-item';
    
    // Insert after the first menu item (Edit account info)
    const firstMenuItem = menuList.children[0];
    if (firstMenuItem && firstMenuItem.nextSibling) {
      menuList.insertBefore(quizResultItem, firstMenuItem.nextSibling);
    } else {
      menuList.appendChild(quizResultItem);
    }
  }
  
  const result = userData.quizResult.result;
  const date = new Date(userData.quizResult.timestamp).toLocaleDateString();
  
  quizResultItem.innerHTML = `
    <span class="menu-icon">🎯</span>
    <div class="menu-text-container">
      <span class="menu-text">Quiz Result: ${result.title}</span>
      <span class="quiz-date">Taken on ${date}</span>
      <div class="quiz-categories">
        ${result.categories.slice(0, 2).map(cat => `<span class="category-mini-tag">${cat}</span>`).join('')}
      </div>
    </div>
  `;
  
  // Add click handler to show full quiz result
  quizResultItem.addEventListener('click', function() {
    showQuizResultModal();
  });
}

// Show quiz result in a modal
function showQuizResultModal() {
  if (!userData.quizResult) return;
  
  const result = userData.quizResult.result;
  const date = new Date(userData.quizResult.timestamp).toLocaleDateString();
  
  // Create modal
  const modal = document.createElement('div');
  modal.className = 'modal quiz-result-modal';
  modal.innerHTML = `
    <div class="modal-content">
      <div class="modal-header">
        <h2>Your Snack Personality</h2>
        <button class="close-button" onclick="closeQuizResultModal()">&times;</button>
      </div>
      <div class="modal-body">
        <div class="quiz-result-display">
          <h3>${result.title}</h3>
          <p>${result.description}</p>
          <p class="quiz-taken-date">Quiz taken on ${date}</p>
          
          <div class="recommended-categories">
            <h4>🛍️ Your Recommended Categories:</h4>
            <div class="category-tags">
              ${result.categories.map(category => `
                <a href="shop.html?category=${encodeURIComponent(category)}" class="category-tag">
                  ${category}
                </a>
              `).join('')}
            </div>
          </div>
          
          <div class="quiz-actions">
            <button class="quiz-btn" onclick="retakeQuiz()">Retake Quiz</button>
            <button class="quiz-btn" onclick="closeQuizResultModal()">Close</button>
          </div>
        </div>
      </div>
    </div>
  `;
  
  document.body.appendChild(modal);
  modal.classList.add('active');
  document.body.style.overflow = 'hidden';
}

// Close quiz result modal
function closeQuizResultModal() {
  const modal = document.querySelector('.quiz-result-modal');
  if (modal) {
    modal.classList.remove('active');
    document.body.style.overflow = 'auto';
    setTimeout(() => {
      document.body.removeChild(modal);
    }, 300);
  }
}

// Retake quiz (redirect to homepage)
function retakeQuiz() {
  window.location.href = 'index.html#quiz';
}

// Update modal display with current data
function updateModalDisplay() {
  const nameInput = document.getElementById('nameInput');
  const emailDisplay = document.querySelector('.email-display');
  const modalProfileImage = document.getElementById('modalProfileImage');
  const modalAvatarPlaceholder = document.getElementById('modalAvatarPlaceholder');
  const removeImageBtn = document.getElementById('removeImageBtn');

  if (!nameInput) return;

  // Update name input
  nameInput.value = userData.name === '(NAME)' ? '' : userData.name;

  // Update email display in modal
  if (emailDisplay) {
    emailDisplay.textContent = userData.email;
  }
  
  // Update modal profile image
  if (userData.profileImage && modalProfileImage && modalAvatarPlaceholder) {
    modalProfileImage.src = userData.profileImage;
    modalProfileImage.style.display = 'block';
    modalAvatarPlaceholder.style.display = 'none';
    if (removeImageBtn) removeImageBtn.style.display = 'inline-block';
  } else if (modalProfileImage && modalAvatarPlaceholder) {
    modalProfileImage.style.display = 'none';
    modalAvatarPlaceholder.style.display = 'flex';
    if (removeImageBtn) removeImageBtn.style.display = 'none';
  }
}

// Open edit account modal
function openEditAccount() {
  const modal = document.getElementById('editAccountModal');
  if (modal) {
    modal.classList.add('active');
    updateModalDisplay();
    
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';
  }
}

// Close edit account modal
function closeEditAccount() {
  const modal = document.getElementById('editAccountModal');
  if (modal) {
    modal.classList.remove('active');
    
    // Restore body scroll
    document.body.style.overflow = 'auto';
  }
}

// Handle image upload
function handleImageUpload(event) {
  const file = event.target.files[0];
  if (!file) return;
  
  // Validate file type
  if (!file.type.startsWith('image/')) {
    alert('Please select a valid image file.');
    return;
  }
  
  // Validate file size (max 5MB)
  if (file.size > 5 * 1024 * 1024) {
    alert('Image size should be less than 5MB.');
    return;
  }
  
  const reader = new FileReader();
  reader.onload = function(e) {
    const imageDataUrl = e.target.result;
    
    // Update modal display
    const modalProfileImage = document.getElementById('modalProfileImage');
    const modalAvatarPlaceholder = document.getElementById('modalAvatarPlaceholder');
    const removeImageBtn = document.getElementById('removeImageBtn');
    
    if (modalProfileImage && modalAvatarPlaceholder) {
      modalProfileImage.src = imageDataUrl;
      modalProfileImage.style.display = 'block';
      modalAvatarPlaceholder.style.display = 'none';
      if (removeImageBtn) removeImageBtn.style.display = 'inline-block';
    }
  };
  
  reader.readAsDataURL(file);
}

// Remove profile image
function removeImage() {
  const modalProfileImage = document.getElementById('modalProfileImage');
  const modalAvatarPlaceholder = document.getElementById('modalAvatarPlaceholder');
  const removeImageBtn = document.getElementById('removeImageBtn');
  const imageUpload = document.getElementById('imageUpload');
  
  // Clear the file input
  if (imageUpload) imageUpload.value = '';
  
  // Update modal display
  if (modalProfileImage && modalAvatarPlaceholder) {
    modalProfileImage.style.display = 'none';
    modalAvatarPlaceholder.style.display = 'flex';
    if (removeImageBtn) removeImageBtn.style.display = 'none';
  }
}

// Save account information
function saveAccountInfo() {
  const nameInput = document.getElementById('nameInput');
  const modalProfileImage = document.getElementById('modalProfileImage');
  
  if (!nameInput) return;
  
  // Validate name
  const newName = nameInput.value.trim();
  if (!newName) {
    alert('Please enter a valid name.');
    return;
  }
  
  // Update user data
  userData.name = newName;
  
  // Update profile image if changed
  if (modalProfileImage && modalProfileImage.style.display === 'block') {
    userData.profileImage = modalProfileImage.src;
  } else {
    userData.profileImage = null;
  }
  
  // Save to localStorage
  saveUserData();
  
  // Update main profile display
  updateProfileDisplay();
  
  // Close modal
  closeEditAccount();
  
  // Show success message
  showNotification('Profile updated successfully!', 'success');
}

// Show notification
function showNotification(message, type = 'info') {
  // Create notification element
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.textContent = message;
  
  // Add styles
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: ${type === 'success' ? '#28a745' : '#17a2b8'};
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    z-index: 2000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    animation: slideInRight 0.3s ease-out;
  `;
  
  // Add animation styles if not already present
  if (!document.getElementById('notificationStyles')) {
    const style = document.createElement('style');
    style.id = 'notificationStyles';
    style.textContent = `
      @keyframes slideInRight {
        from {
          opacity: 0;
          transform: translateX(100px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }
      
      @keyframes slideOutRight {
        from {
          opacity: 1;
          transform: translateX(0);
        }
        to {
          opacity: 0;
          transform: translateX(100px);
        }
      }
      
      .quiz-result-item .menu-text-container {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      }
      
      .quiz-date {
        font-size: 14px;
        color: #a0a0a0;
        font-weight: 400;
      }
      
      .quiz-categories {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
      }
      
      .category-mini-tag {
        background: rgba(88, 47, 14, 0.2);
        color: #582f0e;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 12px;
        font-weight: 500;
      }
      
      .quiz-result-display {
        text-align: center;
      }
      
      .quiz-result-display h3 {
        font-family: 'Playfair Display', serif;
        font-weight: 800;
        color: #582f0e;
        font-size: 24px;
        margin-bottom: 1rem;
      }
      
      .quiz-result-display p {
        font-family: 'Poppins', sans-serif;
        color: #582f0e;
        font-size: 16px;
        line-height: 1.6;
        margin-bottom: 1rem;
      }
      
      .quiz-taken-date {
        font-size: 14px;
        color: #888;
        font-style: italic;
      }
      
      .recommended-categories {
        background: rgba(230, 204, 178, 0.3);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1.5rem 0;
      }
      
      .recommended-categories h4 {
        font-family: 'Playfair Display', serif;
        font-weight: 700;
        color: #582f0e;
        font-size: 18px;
        margin-bottom: 1rem;
      }
      
      .category-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        justify-content: center;
      }
      
      .category-tag {
        background: linear-gradient(135deg, #582f0e 0%, #8b4513 100%);
        color: #e6ccb2;
        padding: 8px 16px;
        border-radius: 20px;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
      }
      
      .category-tag:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(88, 47, 14, 0.3);
      }
      
      .quiz-actions {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-top: 2rem;
      }
      
      .quiz-btn {
        background: linear-gradient(135deg, #582f0e 0%, #8b4513 100%);
        border: none;
        color: #e6ccb2;
        font-family: 'Poppins', sans-serif;
        font-weight: 600;
        font-size: 16px;
        padding: 12px 24px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
      }
      
      .quiz-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(88, 47, 14, 0.3);
      }
    `;
    document.head.appendChild(style);
  }
  
  // Add to DOM
  document.body.appendChild(notification);
  
  // Remove after 3 seconds
  setTimeout(() => {
    notification.style.animation = 'slideOutRight 0.3s ease-out';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 3000);
}

// Close modal when clicking outside
document.addEventListener('click', function(e) {
  const editModal = document.getElementById('editAccountModal');
  const quizModal = document.querySelector('.quiz-result-modal');
  
  if (editModal && e.target === editModal) {
    closeEditAccount();
  }
  
  if (quizModal && e.target === quizModal) {
    closeQuizResultModal();
  }
});

// Handle escape key to close modal
document.addEventListener('keydown', function(e) {
  if (e.key === 'Escape') {
    const editModal = document.getElementById('editAccountModal');
    const quizModal = document.querySelector('.quiz-result-modal');
    
    if (editModal && editModal.classList.contains('active')) {
      closeEditAccount();
    }
    
    if (quizModal && quizModal.classList.contains('active')) {
      closeQuizResultModal();
    }
  }
});

// Add click handlers for other menu items (placeholder functionality)
document.addEventListener('DOMContentLoaded', function() {
  const menuItems = document.querySelectorAll('.menu-item');
  
  menuItems.forEach((item, index) => {
    // Skip the first item (Edit account info) and quiz result item
    if (index !== 0 && !item.classList.contains('quiz-result-item')) {
      item.addEventListener('click', function() {
        const menuText = item.querySelector('.menu-text').textContent;
        showNotification(`${menuText} feature coming soon!`, 'info');
      });
    }
  });
});

// Add smooth scrolling and enhanced interactions
document.addEventListener('DOMContentLoaded', function() {
  // Add hover effects to interactive elements
  const interactiveElements = document.querySelectorAll('.menu-item, .back-button, button');
  
  interactiveElements.forEach(element => {
    element.addEventListener('mouseenter', function() {
      if (this.classList.contains('back-button')) {
        this.style.transform = 'translateX(-5px)';
      } else if (!this.classList.contains('quiz-result-item')) {
        this.style.transform = 'translateX(10px)';
      }
    });
    
    element.addEventListener('mouseleave', function() {
      this.style.transform = 'translateX(0)';
    });
  });
  
  // Add loading state for image uploads
  const imageUpload = document.getElementById('imageUpload');
  if (imageUpload) {
    imageUpload.addEventListener('change', function() {
      if (this.files[0]) {
        const uploadButton = document.querySelector('.upload-button');
        if (uploadButton) {
          const originalText = uploadButton.textContent;
          uploadButton.textContent = 'Processing...';
          uploadButton.disabled = true;
          
          setTimeout(() => {
            uploadButton.textContent = originalText;
            uploadButton.disabled = false;
          }, 1000);
        }
      }
    });
  }
});

// Setup menu handlers
function setupMenuHandlers() {
  // Add click handler for wishlist menu item
  const menuItems = document.querySelectorAll('.menu-item');
  menuItems.forEach((item) => {
    const menuText = item.querySelector('.menu-text').textContent.trim();

    if (menuText === 'Wishlist') {
      item.addEventListener('click', openWishlistModal);
    } else if (menuText === 'Delete Account') {
      item.addEventListener('click', openDeleteAccountModal);
    }
  });
}

// Wishlist functionality
async function openWishlistModal() {
  try {
    const currentUser = localStorage.getItem('currentSnackAtlasUser');
    if (!currentUser) {
      alert('Please login to view your wishlist');
      return;
    }

    const user = JSON.parse(currentUser);
    const response = await fetch(`api/get_wishlist.php?user_id=${user.id}`);
    const result = await response.json();

    if (result.success) {
      displayWishlistModal(result.wishlist);
    } else {
      alert('Failed to load wishlist: ' + result.message);
    }
  } catch (error) {
    console.error('Error loading wishlist:', error);
    alert('Error loading wishlist');
  }
}

function displayWishlistModal(wishlistItems) {
  // Create modal HTML
  const modalHTML = `
    <div id="wishlistModal" class="modal" style="display: flex;">
      <div class="modal-backdrop" onclick="closeWishlistModal()"></div>
      <div class="modal-content" style="max-width: 800px; width: 90%;">
        <button class="modal-close" onclick="closeWishlistModal()">×</button>
        <div class="modal-body">
          <h2 style="margin-bottom: 20px; color: #582f0e;">Your Wishlist</h2>
          <div id="wishlistGrid" class="wishlist-grid">
            ${wishlistItems.length === 0 ?
              '<p style="text-align: center; color: #666; padding: 40px;">Your wishlist is empty</p>' :
              wishlistItems.map(item => createWishlistItemHTML(item)).join('')
            }
          </div>
        </div>
      </div>
    </div>
  `;

  // Remove existing modal if any
  const existingModal = document.getElementById('wishlistModal');
  if (existingModal) {
    existingModal.remove();
  }

  // Add modal to body
  document.body.insertAdjacentHTML('beforeend', modalHTML);
  document.body.style.overflow = 'hidden';
}

function createWishlistItemHTML(item) {
  const imagePath = item.image ? `img/products/${item.image}` : 'img/products/default-snack.png';

  return `
    <div class="wishlist-item" data-product-id="${item.product_id}">
      <div class="wishlist-item-image">
        <img src="${imagePath}" alt="${item.product_name}" onerror="this.src='img/products/default-snack.png'">
      </div>
      <div class="wishlist-item-details">
        <h3 class="wishlist-item-name">${item.product_name}</h3>
        <p class="wishlist-item-description">${item.description || 'No description available'}</p>
        <div class="wishlist-item-price">RM${parseFloat(item.price || 0).toFixed(2)}</div>
        <button class="remove-wishlist-btn" onclick="removeFromWishlist('${item.product_id}')">
          Remove from Wishlist
        </button>
      </div>
    </div>
  `;
}

async function removeFromWishlist(productId) {
  try {
    const currentUser = localStorage.getItem('currentSnackAtlasUser');
    if (!currentUser) return;

    const user = JSON.parse(currentUser);
    const response = await fetch('api/toggle_wishlist.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        user_id: user.id,
        product_id: productId,
        action: 'remove'
      })
    });

    const result = await response.json();

    if (result.success) {
      // Remove item from display
      const itemElement = document.querySelector(`[data-product-id="${productId}"]`);
      if (itemElement) {
        itemElement.remove();
      }

      // Check if wishlist is now empty
      const wishlistGrid = document.getElementById('wishlistGrid');
      if (wishlistGrid && wishlistGrid.children.length === 0) {
        wishlistGrid.innerHTML = '<p style="text-align: center; color: #666; padding: 40px;">Your wishlist is empty</p>';
      }

      showNotification('Removed from wishlist', 'success');
    } else {
      alert('Failed to remove item: ' + result.message);
    }
  } catch (error) {
    console.error('Error removing from wishlist:', error);
    alert('Error removing item from wishlist');
  }
}

function closeWishlistModal() {
  const modal = document.getElementById('wishlistModal');
  if (modal) {
    modal.remove();
    document.body.style.overflow = 'auto';
  }
}

// Delete account functionality
function openDeleteAccountModal() {
  const modalHTML = `
    <div id="deleteAccountModal" class="modal" style="display: flex;">
      <div class="modal-backdrop" onclick="closeDeleteAccountModal()"></div>
      <div class="modal-content" style="max-width: 500px; width: 90%;">
        <button class="modal-close" onclick="closeDeleteAccountModal()">×</button>
        <div class="modal-body">
          <h2 style="margin-bottom: 20px; color: #d32f2f;">Delete Account</h2>
          <p style="margin-bottom: 20px; color: #666;">
            This action cannot be undone. All your data including wishlist, orders, and account information will be permanently deleted.
          </p>
          <div class="form-group">
            <label for="deletePassword" class="form-label">Enter your password to confirm:</label>
            <input type="password" id="deletePassword" class="form-input" placeholder="Password" required>
          </div>
          <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
            <button onclick="closeDeleteAccountModal()" class="cancel-button">Cancel</button>
            <button onclick="confirmDeleteAccount()" class="delete-button" style="background: #d32f2f; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
              Delete Account
            </button>
          </div>
        </div>
      </div>
    </div>
  `;

  // Remove existing modal if any
  const existingModal = document.getElementById('deleteAccountModal');
  if (existingModal) {
    existingModal.remove();
  }

  // Add modal to body
  document.body.insertAdjacentHTML('beforeend', modalHTML);
  document.body.style.overflow = 'hidden';
}

async function confirmDeleteAccount() {
  const passwordInput = document.getElementById('deletePassword');
  const password = passwordInput.value.trim();

  if (!password) {
    alert('Please enter your password to confirm deletion');
    return;
  }

  if (!confirm('Are you absolutely sure you want to delete your account? This action cannot be undone.')) {
    return;
  }

  try {
    const currentUser = localStorage.getItem('currentSnackAtlasUser');
    if (!currentUser) {
      alert('User session not found');
      return;
    }

    const user = JSON.parse(currentUser);
    const response = await fetch('api/delete_user.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        user_id: user.id,
        password: password
      })
    });

    const result = await response.json();

    if (result.success) {
      alert('Account deleted successfully');
      // Clear localStorage and redirect to login
      localStorage.removeItem('currentSnackAtlasUser');
      localStorage.removeItem('profileData');
      localStorage.removeItem('quizResult');
      window.location.href = 'login.html';
    } else {
      alert('Failed to delete account: ' + result.message);
    }
  } catch (error) {
    console.error('Error deleting account:', error);
    alert('Error deleting account');
  }
}

function closeDeleteAccountModal() {
  const modal = document.getElementById('deleteAccountModal');
  if (modal) {
    modal.remove();
    document.body.style.overflow = 'auto';
  }
}