<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Log all POST data
file_put_contents('register_debug.log', date('Y-m-d H:i:s') . " - POST data: " . print_r($_POST, true) . "\n", FILE_APPEND);

// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "group4_db";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    file_put_contents('register_debug.log', date('Y-m-d H:i:s') . " - Connection failed: " . $conn->connect_error . "\n", FILE_APPEND);
    die("Connection failed: " . $conn->connect_error);
}

file_put_contents('register_debug.log', date('Y-m-d H:i:s') . " - Connection successful\n", FILE_APPEND);

// Check if form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    file_put_contents('register_debug.log', date('Y-m-d H:i:s') . " - POST request received\n", FILE_APPEND);
    
    // Get form data
    $firstName = isset($_POST['firstName']) ? $_POST['firstName'] : '';
    $lastName = isset($_POST['lastName']) ? $_POST['lastName'] : '';
    $username = isset($_POST['username']) ? $_POST['username'] : '';
    $email = isset($_POST['email']) ? $_POST['email'] : '';
    $phone = isset($_POST['phone']) ? $_POST['phone'] : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    
    file_put_contents('register_debug.log', date('Y-m-d H:i:s') . " - Extracted data: firstName=$firstName, lastName=$lastName, username=$username, email=$email\n", FILE_APPEND);
    
    // Hash the password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    try {
        // Check if username or email already exists
        $checkSql = "SELECT * FROM users WHERE username = ? OR email = ?";
        $checkStmt = $conn->prepare($checkSql);
        
        if (!$checkStmt) {
            throw new Exception("Prepare failed: " . $conn->error);
        }
        
        $checkStmt->bind_param("ss", $username, $email);
        $checkStmt->execute();
        $result = $checkStmt->get_result();
        
        if ($result->num_rows > 0) {
            $user = $result->fetch_assoc();
            if ($user['email'] == $email) {
                echo json_encode(['success' => false, 'message' => 'Email already registered']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Username already taken']);
            }
        } else {
            // Insert new user
            $sql = "INSERT INTO users (first_name, last_name, username, email, phone, password) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            
            $stmt = $conn->prepare($sql);
            
            if (!$stmt) {
                throw new Exception("Prepare failed: " . $conn->error);
            }
            
            $stmt->bind_param("ssssss", $firstName, $lastName, $username, $email, $phone, $hashedPassword);
            
            if ($stmt->execute()) {
                // Start session and store user data
                session_start();
                $_SESSION['user_id'] = $conn->insert_id;
                $_SESSION['username'] = $username;
                $_SESSION['first_name'] = $firstName;
                $_SESSION['last_name'] = $lastName;
                $_SESSION['email'] = $email;
                
                echo json_encode([
                    'success' => true, 
                    'message' => 'Registration successful!',
                    'userId' => $conn->insert_id,
                    'username' => $username,
                    'firstName' => $firstName,
                    'lastName' => $lastName,
                    'email' => $email
                ]);
            } else {
                throw new Exception("Execute failed: " . $stmt->error);
            }
            
            $stmt->close();
        }
        
        $checkStmt->close();
    } catch (Exception $e) {
        file_put_contents('register_debug.log', date('Y-m-d H:i:s') . " - Error: " . $e->getMessage() . "\n", FILE_APPEND);
        echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
    }
} else {
    file_put_contents('register_debug.log', date('Y-m-d H:i:s') . " - Not a POST request\n", FILE_APPEND);
    echo json_encode(['success' => false, 'message' => 'This script only accepts POST requests.']);
}

$conn->close();
?>

