@import url("https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700;800;900&display=swap");
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: #e6ccb2;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #582f0e 0%, #8b4513 100%);
  border-radius: 6px;
  border: 2px solid #e6ccb2;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #8b4513 0%, #582f0e 100%);
}

::-webkit-scrollbar-corner {
  background: #e6ccb2;
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: #582f0e #e6ccb2;
}

body {
  font-family: 'Poppins', sans-serif;
  background: white;
  overflow-x: hidden;
}

.container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  background: white;
}

/* Background Images */
.bg-image {
  position: absolute;
  width: 100vw;
  height: 5000px;
  object-fit: cover;
  z-index: 1;
}

.bg-1 {
  top: 0;
  left: 0;
  min-height: 100vh;
}

.bg-2 {
  top: 2066px;
  left: 0;
  min-height: 80vh;
}

/* Navigation */
.navbar {
  position: relative;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 80px 0; /* Reduced from 46px to 20px */
  width: 100%;
  transition: all 0.3s ease;
}

.navbar.sticky {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(230, 204, 178, 0.95);
  backdrop-filter: blur(10px);
  padding: 12px 80px; /* Reduced from 20px to 12px */
  box-shadow: 0 2px 20px rgba(88, 47, 14, 0.2);
  border-bottom: 2px solid rgba(88, 47, 14, 0.1);
}

.nav-left .logo {
  width: 60px; /* Reduced from 80px */
  height: 60px; /* Reduced from 80px */
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: drop-shadow(0 0 1rem #582f0e);
  transition: transform 0.3s ease;
}

.navbar.sticky .nav-left .logo {
  width: 45px; /* Reduced from 60px */
  height: 45px; /* Reduced from 60px */
}

.nav-center {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.nav-links {
  display: flex;
  align-items: center;
  list-style: none;
  gap: 0;
}

.nav-link {
  font-family: 'Poppins', sans-serif;
  color: #582f0e;
  font-size: 22px; /* Reduced from 28px */
  text-decoration: none;
  font-weight: 600;
  letter-spacing: 0.48px;
  transition: all 0.3s ease;
  padding: 6px 10px; /* Reduced from 8px 12px */
  border-radius: 6px;
}

.nav-link:hover {
  font-weight: 700;
  text-decoration: underline;
  transform: translateY(-2px);
  background: rgba(88, 47, 14, 0.1);
}

.nav-link.active {
  font-weight: 700;
  text-decoration: underline;
  background: rgba(88, 47, 14, 0.15);
}

.nav-separator {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  color: #582f0e;
  font-size: 22px; /* Reduced from 28px */
  margin: 0 8px; /* Reduced from 0 12px */
}

/* Dropdown Menu */
.dropdown {
  position: relative;
}

.dropdown-toggle::after {
  content: ' ▼';
  font-size: 16px;
  margin-left: 8px;
  transition: transform 0.3s ease;
}

.dropdown:hover .dropdown-toggle::after {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(230, 204, 178, 0.98);
  backdrop-filter: blur(15px);
  border-radius: 12px;
  padding: 16px 0;
  min-width: 200px;
  box-shadow: 0 8px 32px rgba(88, 47, 14, 0.3);
  border: 2px solid rgba(88, 47, 14, 0.2);
  opacity: 0;
  visibility: hidden;
  transform: translateX(-50%) translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1000;
}

.dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

.dropdown-item {
  display: block;
  padding: 12px 24px;
  color: #582f0e;
  text-decoration: none;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 18px;
  transition: all 0.3s ease;
}

.dropdown-item:hover {
  background: rgba(88, 47, 14, 0.15);
  color: #8b4513;
  transform: translateX(8px);
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 12px; /* Reduced from default */
}

.nav-icon {
  width: 24px; /* Reduced if needed */
  height: 24px; /* Reduced if needed */
  color: #582f0e;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.nav-icon:hover {
  transform: scale(1.15);
}

.nav-icon img {
  width: 100%;
  height: 100%;
  filter: brightness(0) saturate(100%) invert(25%) sepia(45%) saturate(1200%) hue-rotate(15deg);
}

.clock {
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  color: #582f0e;
  font-size: 16px; /* Reduced if needed */
  background: rgba(230, 204, 178, 0.5);
  padding: 6px 12px; /* Reduced if needed */
  border-radius: 20px;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

/* Hero Section */
.hero-section {
  position: relative;
  width: 100%;
  height: 585px;
  margin-top: 112px;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.hero-image-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.hero-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 1s ease-in-out;
}

.hero-image.active {
  opacity: 1;
}

.hero-text {
  text-align: center;
  z-index: 2;
}

.hero-title {
  width: 644px;
  font-family: 'Playfair Display', serif;
  font-weight: 800;
  color: #e6ccb2;
  font-size: 110px;
  text-align: center;
  line-height: normal;
  -webkit-text-stroke: 1.5px #000000;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-description {
  width: 681px;
  margin-top: 32px;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  color: #e6ccb2;
  font-size: 22px;
  text-align: center;
  line-height: 1.4;
  -webkit-text-stroke: 0.4px #000000;
}

.hero-button {
  margin-top: 40px;
  background: transparent;
  border: none;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  color: #ede0d4;
  font-size: 22px;
  text-align: center;
  letter-spacing: 0.44px;
  line-height: normal;
  white-space: nowrap;
  cursor: pointer;
  -webkit-text-stroke: 0.3px #000000;
  transition: transform 0.2s ease;
  padding: 12px 24px;
  border-radius: 8px;
}

.hero-button:hover {
  transform: scale(1.05);
}

/* Headline Section */
.headline-section {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 156px;
}

.section-title, .headline-title {
  font-family: 'Playfair Display', serif;
  font-weight: 800;
  color: #e6ccb2;
  font-size: 110px;
  text-align: center;
  letter-spacing: 1.65px;
  line-height: normal;
  -webkit-text-stroke: 1.5px #000000;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.section-title { margin-left: 20px; }

.headline-description, .section-description {
  width: 698px;
  margin-top: 32px;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  color: #504840;
  font-size: 20px;
  text-align: center;
  letter-spacing: 0.3px;
  line-height: 1.4;
  -webkit-text-stroke: 0.3px #000000;
}

.section-description { margin-left: 100px; }

/* Quiz Section */
.quiz-container {
  margin-top: 48px;
  display: flex;
  justify-content: center;
}

.quiz-button {
  background: linear-gradient(135deg, #582f0e 0%, #8b4513 100%);
  border: none;
  color: #e6ccb2;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  font-size: 24px;
  padding: 20px 40px;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 24px rgba(88, 47, 14, 0.3);
  -webkit-text-stroke: 0.5px #000000;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.quiz-button:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 12px 32px rgba(88, 47, 14, 0.4);
  background: linear-gradient(135deg, #8b4513 0%, #582f0e 100%);
}

/* About Section */
.about-section {
  position: relative;
  z-index: 10;
  margin-top: 224px;
}

.border-container {
  width: 100%;
  height: 57px;
  display: flex;
}

.border-image {
  width: 50%;
  object-fit: cover;
  z-index: 1;
}

.about-content {
  position: relative;
  width: 100%;
  height: 634px;
  display: flex;
}

.about-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.about-inner {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 138px;
}

.about-image {
  width: 473px;
  height: 468px;
  object-fit: cover;
}

.about-text {
  margin-left: 127px;
}

.about-text .section-title {
  width: auto;
}

.about-text .section-description {
  width: 562px;
  text-align: left;
}

/* Shop Section */
.shop-section {
  position: relative;
  z-index: 10;
  margin-top: 108px;
  padding: 0 80px;
}

.shop-section .section-title,
.shop-section .section-description {
  margin-left: auto;
  margin-right: auto;
}

/* Featured Products Carousel */
.carousel-container {
  position: relative;
  width: 100%;
  max-width: 1200px;
  margin: 40px auto;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.carousel-btn {
  position: absolute;
  z-index: 20;
  background: rgba(230, 204, 178, 0.9);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-btn:hover {
  transform: scale(1.1);
  background: rgba(230, 204, 178, 1);
  box-shadow: 0 4px 12px rgba(88, 47, 14, 0.3);
}

.carousel-prev {
  left: 10px;
}

.carousel-next {
  right: 10px;
}

.carousel-btn svg {
  width: 24px;
  height: 24px;
  color: #582f0e;
}

.products-container {
  display: flex;
  gap: 32px;
  transition: transform 0.5s ease;
  padding: 20px 0;
  width: 100%;
}

.product-card {
  flex: 0 0 auto;
  width: 300px;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 25px rgba(88, 47, 14, 0.2);
  background: rgba(255, 255, 255, 0.35);
  border-color: rgba(230, 204, 178, 0.7);
}

.product-image-container {
  width: 100%;
  height: 180px;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 16px;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.product-card:hover .product-image {
  transform: scale(1.05);
}

.product-name {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #582f0e;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
}

.product-description {
  font-size: 14px;
  color: #333;
  margin-bottom: 12px;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3);
}

.product-price {
  font-size: 16px;
  font-weight: 600;
  color: #582f0e;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
}

/* Shop button container */
.shop-button-container {
  margin-top: 40px;
  text-align: center;
}

.shop-button {
  background: #582f0e;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 30px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.shop-button:hover {
  background: #7f4f24;
  transform: translateY(-3px);
  box-shadow: 0 4px 15px rgba(88, 47, 14, 0.3);
}

/* Contact Section */
.contact-section {
  position: relative;
  z-index: 10;
  margin-top: 100px; /* Increased from 108px to push it lower */
  margin-bottom: 0; /* Removed bottom margin to eliminate blank space */
  padding-bottom: 0; /* Removed padding at bottom to eliminate blank space */
}

.contact-bg {
  width: 100%;
  height: 100%; /* Changed to 100% to fit content exactly */
  min-height: 600px; /* Minimum height to ensure proper coverage */
  object-fit: cover;
  object-position: center; /* Ensure proper cropping from center */
}

.contact-content {
  position: absolute;
  top: 50%; /* Reset to center position */
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 90%;
  max-width: 1200px;
  padding-top: 600px; /* Reduced padding to fit better */
  padding-bottom: 40px; /* Added bottom padding for proper spacing */
}

.contact-logo {
  width: 53px;
  height: 49px;
}

.contact-content .section-title {
  margin-top: 32px;
  width: auto;
}

.contact-social {
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.social-link {
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  color: #e6ccb2;
  font-size: 20px;
  text-decoration: none;
  letter-spacing: 0.3px;
  -webkit-text-stroke: 1px #000000;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
  transition: all 0.3s ease;
  padding: 8px 12px;
  border-radius: 6px;
}

.social-link:hover {
  color: #ede0d4;
  transform: translateY(-2px);
  background: rgba(88, 47, 14, 0.2);
}

.social-separator {
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  color: #e6ccb2;
  font-size: 20px;
  -webkit-text-stroke: 1px #000000;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
}

.contact-main {
  display: flex;
  gap: 40px;
  margin-top: 40px;
  width: 100%;
  max-width: 1000px;
}

.map-container {
  flex: 1;
  height: 400px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(88, 47, 14, 0.3);
  border: 3px solid rgba(230, 204, 178, 0.5);
}

.contact-form-container {
  flex: 1;
  background: rgba(230, 204, 178, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(88, 47, 14, 0.3);
  border: 2px solid rgba(88, 47, 14, 0.2);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  color: #582f0e;
  font-size: 16px;
}

.form-group input,
.form-group textarea {
  padding: 16px;
  border: 2px solid rgba(88, 47, 14, 0.3);
  border-radius: 12px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #582f0e;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #582f0e;
  box-shadow: 0 0 0 3px rgba(88, 47, 14, 0.1);
}

.submit-button {
  background: linear-gradient(135deg, #582f0e 0%, #8b4513 100%);
  border: none;
  color: #e6ccb2;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  font-size: 18px;
  padding: 16px 32px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(88, 47, 14, 0.3);
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(88, 47, 14, 0.4);
  background: linear-gradient(135deg, #8b4513 0%, #582f0e 100%);
}

.contact-success-message {
  margin-top: 16px;
  padding: 16px;
  background: rgba(40, 167, 69, 0.9);
  color: white;
  border-radius: 12px;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  text-align: center;
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.contact-button {
  margin-top: 32px;
  background: transparent;
  border: none;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  color: #ede0d4;
  font-size: 22px;
  text-align: center;
  letter-spacing: 0.44px;
  line-height: normal;
  white-space: nowrap;
  cursor: pointer;
  -webkit-text-stroke: 0.3px #000000;
  transition: all 0.3s ease;
  padding: 16px 32px;
  border-radius: 8px;
}

.contact-button:hover {
  transform: scale(1.05);
}

.contact-footer {
  margin-top: 48px;
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  color: #e6ccb2;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0.24px;
  line-height: normal;
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #582f0e 0%, #8b4513 100%);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all 0.3s ease;
  box-shadow: 0 8px 24px rgba(88, 47, 14, 0.3);
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.back-to-top:hover {
  transform: translateY(-4px) scale(1.1);
  box-shadow: 0 12px 32px rgba(88, 47, 14, 0.4);
}

.back-to-top svg {
  width: 24px;
  height: 24px;
  color: #e6ccb2;
}

/* Quiz Modal */
.quiz-modal {
  display: none;
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
}

.quiz-modal.active {
  display: flex;
  align-items: center;
  justify-content: center;
}

.quiz-content {
  background: linear-gradient(135deg, #f5f1eb 0%, #e8ddd4 100%);
  border-radius: 20px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

.quiz-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 2px solid #d4c4b0;
}

.quiz-header h2 {
  font-family: 'Playfair Display', serif;
  font-weight: 800;
  color: #582f0e;
  font-size: 28px;
  margin: 0;
}

.close-quiz {
  background: none;
  border: none;
  font-size: 32px;
  color: #582f0e;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.close-quiz:hover {
  background-color: rgba(88, 47, 14, 0.1);
}

.quiz-body {
  padding: 2rem;
}

.quiz-question {
  margin-bottom: 2rem;
}

.quiz-question h3 {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  color: #582f0e;
  font-size: 20px;
  margin-bottom: 1rem;
}

.quiz-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quiz-option {
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid #d4c4b0;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Poppins', sans-serif;
  color: #582f0e;
}

.quiz-option:hover {
  background: rgba(88, 47, 14, 0.1);
  border-color: #582f0e;
  transform: translateX(8px);
}

.quiz-option.selected {
  background: rgba(88, 47, 14, 0.2);
  border-color: #582f0e;
}

.quiz-result {
  text-align: center;
  padding: 2rem;
}

.quiz-result h3 {
  font-family: 'Playfair Display', serif;
  font-weight: 800;
  color: #582f0e;
  font-size: 24px;
  margin-bottom: 1rem;
}

.quiz-result p {
  font-family: 'Poppins', sans-serif;
  color: #582f0e;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.quiz-suggestions {
  background: rgba(230, 204, 178, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1.5rem 0;
}

.quiz-suggestions h4 {
  font-family: 'Playfair Display', serif;
  font-weight: 700;
  color: #582f0e;
  font-size: 18px;
  margin-bottom: 1rem;
}

.suggestion-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.category-tag {
  background: linear-gradient(135deg, #582f0e 0%, #8b4513 100%);
  color: #e6ccb2;
  padding: 8px 16px;
  border-radius: 20px;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.category-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(88, 47, 14, 0.3);
}

.quiz-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 2rem;
}

.quiz-btn {
  background: linear-gradient(135deg, #582f0e 0%, #8b4513 100%);
  border: none;
  color: #e6ccb2;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  font-size: 16px;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quiz-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(88, 47, 14, 0.3);
}

/* Newsletter Modal */
.newsletter-modal {
  display: none;
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
}

.newsletter-modal.active {
  display: flex;
  align-items: center;
  justify-content: center;
}

.newsletter-content {
  background: linear-gradient(135deg, #f5f1eb 0%, #e8ddd4 100%);
  border-radius: 20px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

.newsletter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 2px solid #d4c4b0;
}

.newsletter-header h2 {
  font-family: 'Playfair Display', serif;
  font-weight: 800;
  color: #582f0e;
  font-size: 24px;
  margin: 0;
}

.close-newsletter {
  background: none;
  border: none;
  font-size: 32px;
  color: #582f0e;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.close-newsletter:hover {
  background-color: rgba(88, 47, 14, 0.1);
}

.newsletter-body {
  padding: 2rem;
  text-align: center;
}

.newsletter-body p {
  font-family: 'Poppins', sans-serif;
  color: #582f0e;
  font-size: 16px;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.newsletter-form {
  display: flex;
  gap: 12px;
}

.newsletter-form input {
  flex: 1;
  padding: 16px;
  border: 2px solid #d4c4b0;
  border-radius: 12px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #582f0e;
}

.newsletter-form button {
  background: linear-gradient(135deg, #582f0e 0%, #8b4513 100%);
  border: none;
  color: #e6ccb2;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  font-size: 16px;
  padding: 16px 24px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.newsletter-form button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(88, 47, 14, 0.3);
}

/* Loading Animation */
.loading {
  width: 100%;
  text-align: center;
  padding: 40px;
  font-size: 18px;
  color: #582f0e;
}

.loading::after {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #e6ccb2;
  border-top: 2px solid #582f0e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Fade-in animations for sections */
.fade-in-section {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.fade-in-section.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .navbar {
    padding: 46px 40px 0;
  }
  
  .navbar.sticky {
    padding: 20px 40px;
  }
  
  .shop-section {
    padding: 0 40px;
  }
  
  .hero-title {
    font-size: 80px;
    width: 500px;
  }
  
  .hero-description {
    width: 500px;
    font-size: 18px;
  }
  
  .section-title {
    font-size: 80px;
    width: 500px;
  }
  
  .section-description {
    width: 500px;
    font-size: 18px;
  }
  
  .contact-main {
    flex-direction: column;
    gap: 24px;
  }
  
  .map-container {
    height: 300px;
  }
}

@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    gap: 20px;
    padding: 20px;
  }
  
  .navbar.sticky {
    padding: 15px 20px;
  }
  
  .nav-center {
    position: static;
    transform: none;
  }
  
  .nav-links {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .nav-link {
    font-size: 20px;
  }
  
  .nav-separator {
    font-size: 20px;
  }
  
  .clock {
    font-size: 18px;
  }
  
  .hero-title {
    font-size: 60px;
    width: 90%;
  }
  
  .hero-description {
    width: 90%;
    font-size: 16px;
  }
  
  .section-title {
    font-size: 60px;
    width: 90%;
  }
  
  .section-description {
    width: 90%;
    font-size: 16px;
  }
  
  .about-inner {
    flex-direction: column;
    padding: 40px 20px;
    text-align: center;
  }
  
  .about-text {
    margin-left: 0;
    margin-top: 40px;
  }
  
  .products-container {
    gap: 20px;
    padding: 20px 40px;
  }
  
  .product-card {
    min-width: 250px;
  }
  
  .carousel-prev {
    left: -10px;
  }
  
  .carousel-next {
    right: -10px;
  }
  
  .contact-bg {
    height: 100%; /* Changed to 100% to fit content exactly */
    min-height: 800px; /* Minimum height for mobile to ensure proper coverage */
  }
  
  .quiz-content,
  .newsletter-content {
    width: 95%;
    margin: 1rem;
  }
  
  .newsletter-form {
    flex-direction: column;
  }
}
