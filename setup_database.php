<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Database Setup</h1>";

// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "group4_db";

// Create connection
$conn = new mysqli($servername, $username, $password);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}
echo "Connected to MySQL server successfully<br>";

// Check if database exists, create if not
$result = $conn->query("SHOW DATABASES LIKE '$dbname'");
if ($result->num_rows == 0) {
    echo "Database '$dbname' does not exist. Creating it now...<br>";
    
    if ($conn->query("CREATE DATABASE $dbname")) {
        echo "Database created successfully<br>";
    } else {
        die("Error creating database: " . $conn->error);
    }
} else {
    echo "Database '$dbname' already exists<br>";
}

// Select the database
$conn->select_db($dbname);

// Check if users table exists, create if not
$result = $conn->query("SHOW TABLES LIKE 'users'");
if ($result->num_rows == 0) {
    echo "Table 'users' does not exist. Creating it now...<br>";
    
    $sql = "CREATE TABLE users (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        phone VARCHAR(20),
        password VARCHAR(255) NOT NULL,
        registration_date DATETIME DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql)) {
        echo "Table 'users' created successfully<br>";
    } else {
        die("Error creating table: " . $conn->error);
    }
} else {
    echo "Table 'users' already exists<br>";
}

echo "<h2>Database setup complete!</h2>";
echo "<p>You can now <a href='login.html'>login</a> or <a href='login.html'>register</a>.</p>";

$conn->close();
?>