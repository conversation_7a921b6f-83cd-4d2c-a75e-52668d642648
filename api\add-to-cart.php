<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);

$host = 'localhost';
$dbname = 'group4_db';
$username = 'root';
$password = ''; // update if needed

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $data = json_decode(file_get_contents("php://input"), true);

    if (!$data) {
        echo json_encode(['success' => false, 'message' => 'No data received']);
        exit;
    }

    $stmt = $pdo->prepare("INSERT INTO cart (id, name, description, price, image) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute([
        $data['id'],
        $data['name'],
        $data['description'],
        $data['price'],
        $data['image']
    ]);

    echo json_encode(['success' => true, 'message' => 'Item added to cart!']);
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?>
