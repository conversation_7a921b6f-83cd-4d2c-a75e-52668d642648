2025-06-29 19:01:20 - Request received
2025-06-29 19:01:20 - Raw input: {"user_id":null,"product_id":"1","quantity":1}
2025-06-29 19:01:20 - Decoded data: Array
(
    [user_id] => 
    [product_id] => 1
    [quantity] => 1
)

2025-06-29 19:01:20 - Processed data: productId=1, userId=, quantity=1
2025-06-29 19:01:20 - Existing item: No
2025-06-29 19:01:20 - Insert result: Success
2025-06-29 19:01:20 - Response: {"success":true,"message":"Item added to cart successfully","data":{"product_id":"1","user_id":null,"quantity":1}}
2025-06-29 19:04:17 - Request received
2025-06-29 19:04:17 - Raw input: {"user_id":3,"product_id":"F2","quantity":1}
2025-06-29 19:14:34 - Request received
2025-06-29 19:14:34 - Raw input: {"product_id":"F3","quantity":1}
2025-06-29 20:31:19 - Request received
2025-06-29 20:31:19 - Raw input: {"product_id":"F2","quantity":2}
2025-06-30 03:05:54 - Request received
2025-06-30 03:05:54 - Raw input: {"product_id":"F3","quantity":1}
2025-06-30 03:05:58 - Request received
2025-06-30 03:05:58 - Raw input: {"product_id":"P2","quantity":2}
2025-06-30 03:12:33 - Request received
2025-06-30 03:12:33 - Raw input: {"product_id":"F1","quantity":1}
2025-06-30 04:14:55 - Request received
2025-06-30 04:14:55 - Raw input: {"product_id":"F3","quantity":3}
2025-06-30 09:15:09 - Request received
2025-06-30 09:15:09 - Raw input: {"product_id":"P1","quantity":2}
2025-06-30 09:15:11 - Request received
2025-06-30 09:15:11 - Raw input: {"product_id":"F3","quantity":1}
