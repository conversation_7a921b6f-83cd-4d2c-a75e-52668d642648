<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SnackAtlas | Login</title>
    <link rel="stylesheet" href="css/login.css">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="login-wrapper">
            <!-- Logo Section -->
            <div class="logo-section">
                <div class="logo">
                    <i class="fas fa-cookie-bite"></i>
                </div>
                <h1 class="brand-title">Snack Atlas</h1>
                <p class="brand-subtitle">Discover flavors from around the world</p>
            </div>

            <!-- Main Card -->
            <div class="auth-card">
                <!-- Toggle Buttons -->
                <div class="auth-toggle">
                    <button type="button" id="loginBtn" class="toggle-btn active">Sign In</button>
                    <button type="button" id="registerBtn" class="toggle-btn">Sign Up</button>
                </div>

                <!-- Login Form -->
                <form id="loginForm" class="auth-form active">
                    <div class="form-group">
                        <div class="input-container">
                            <i class="fas fa-envelope input-icon"></i>
                            <input type="email" id="loginEmail" name="email" placeholder="Email Address" required>
                        </div>
                        <span class="error-message" id="loginEmailError"></span>
                    </div>

                    <div class="form-group">
                        <div class="input-container">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password" id="loginPassword" name="password" placeholder="Password" required>
                            <button type="button" class="password-toggle" id="loginPasswordToggle">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <span class="error-message" id="loginPasswordError"></span>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="rememberMe">
                            <span class="checkmark"></span>
                            Remember me
                        </label>
                        <a href="#" class="forgot-password">Forgot password?</a>
                    </div>

                    <button type="submit" class="submit-btn">Sign In</button>
                </form>

                <!-- Register Form -->
                <form id="registerForm" class="auth-form" method="POST">
                    <div class="form-row">
                        <div class="form-group half">
                            <div class="input-container">
                                <i class="fas fa-user input-icon"></i>
                                <input type="text" id="firstName" name="firstName" placeholder="First Name" required>
                            </div>
                            <span class="error-message" id="firstNameError"></span>
                        </div>
                        <div class="form-group half">
                            <div class="input-container">
                                <i class="fas fa-user input-icon"></i>
                                <input type="text" id="lastName" name="lastName" placeholder="Last Name" required>
                            </div>
                            <span class="error-message" id="lastNameError"></span>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="input-container">
                            <i class="fas fa-user-circle input-icon"></i>
                            <input type="text" id="username" name="username" placeholder="Username" required>
                        </div>
                        <span class="error-message" id="usernameError"></span>
                    </div>

                    <div class="form-group">
                        <div class="input-container">
                            <i class="fas fa-envelope input-icon"></i>
                            <input type="email" id="registerEmail" name="email" placeholder="Email Address" required>
                        </div>
                        <span class="error-message" id="registerEmailError"></span>
                    </div>

                    <div class="form-group">
                        <div class="input-container">
                            <i class="fas fa-phone input-icon"></i>
                            <input type="tel" id="phone" name="phone" placeholder="Phone Number" required>
                        </div>
                        <span class="error-message" id="phoneError"></span>
                    </div>

                    <div class="form-group">
                        <div class="input-container">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password" id="registerPassword" name="password" placeholder="Password" required>
                            <button type="button" class="password-toggle" id="registerPasswordToggle">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <span class="error-message" id="registerPasswordError"></span>
                    </div>

                    <div class="form-group">
                        <div class="input-container">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password" id="confirmPassword" name="confirmPassword" placeholder="Confirm Password" required>
                            <button type="button" class="password-toggle" id="confirmPasswordToggle">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <span class="error-message" id="confirmPasswordError"></span>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="agreeTerms" required>
                            <span class="checkmark"></span>
                            I agree to the <a href="#" class="terms-link"> Terms & Conditions</a>
                        </label>
                    </div>

                    <button type="submit" class="submit-btn" id="registerSubmitBtn">Create Account</button>
                </form>

                <!-- Divider -->
                <div class="divider">
                    <span>or</span>
                </div>

                <!-- Google Login -->
                <button type="button" id="googleLoginBtn" class="google-btn">
                    <i class="fab fa-google"></i>
                    Continue with Google
                </button>

                <!-- Success Message -->
                <div id="successMessage" class="success-message">
                    <i class="fas fa-check-circle"></i>
                    <span id="successText"></span>
                </div>
            </div>
        </div>
    </div>

    <script src="js/login.js"></script>
</body>
</html>
