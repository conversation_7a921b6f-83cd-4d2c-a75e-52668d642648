/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #f5f5dc; /* Khaki background */
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
}

.bg-image {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    object-fit: cover;
    z-index: -10;
}

/* Navigation space */
.nav-space {
    height: 80px;
    background-color: #e6e6d4; /* Slightly darker khaki for nav area */
    border-bottom: 2px solid #ddd;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

.content-wrapper {
    width: 100%;
    position: relative;
    padding: 0 20px;
    z-index: 5;
}

/* Header */
.header {
    position: relative;
    z-index: 10;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 46px 80px 0;
    width: 100%;
}

.nav-left .logo {
    width: 53px;
    height: 49px;
    border-radius: 8px;
}

.nav-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.nav-links {
    display: flex;
    align-items: center;
    list-style: none;
    gap: 0;
}

.nav-link {
    font-family: 'Poppins', sans-serif;
    color: #582f0e;
    font-size: 24px;
    text-decoration: none;
    font-weight: 600;
    letter-spacing: 0.48px;
    transition: all 0.3s ease;
}

.nav-link:hover {
    font-weight: 700;
    text-decoration: underline;
    transform: translateY(-2px);
}

.nav-link.active {
    font-weight: 700;
    text-decoration: underline;
}

.nav-separator {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: #582f0e;
    font-size: 24px;
    margin: 0 8px;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.nav-icon {
    width: 28px;
    height: 24px;
    color: #582f0e;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.nav-icon:hover {
    transform: scale(1.1);
}

.clock {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: #582f0e;
    font-size: 24px;
    letter-spacing: 0.24px;
    min-width: 120px;
    text-align: center;
}

/* Page Title */
.page-title {
    font-family: 'Playfair Display', serif;
    font-weight: 800;
    font-size: 100px;
    color: #e6ccb2;
    -webkit-text-stroke: 2px #000000;
    text-align: center;
    margin: 9px 0 61px;
    letter-spacing: 0;
}

/* Container */
.container1 {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    margin-top: 80px; /* Account for fixed nav */
    z-index: 20;
}

/* Container */
.container1 {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.checkout-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #ffc0cb; /* Light pink border */
}

.checkout-header h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #8b4513; /* Darker brown for contrast */
}

.back-to-cart-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #ffc0cb; /* Light pink */
    color: #8b4513;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.back-to-cart-btn:hover {
    background-color: #ffb6c1; /* Slightly darker pink on hover */
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 192, 203, 0.3);
}

/* Checkout content layout */
.checkout-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
}

/* Order Summary Section */
.order-summary-section {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 2px solid #ffc0cb;
}

.order-summary-header {
    background-color: #ffc0cb;
    padding: 20px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    user-select: none;
    transition: background-color 0.3s ease;
}

.order-summary-header:hover {
    background-color: #ffb6c1;
}

.order-summary-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #8b4513;
}

.toggle-icon {
    font-size: 1.5rem;
    font-weight: bold;
    color: #8b4513;
    transition: transform 0.3s ease;
}

.toggle-icon.collapsed {
    transform: rotate(180deg);
}

.order-summary-content {
    padding: 20px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.order-summary-content.collapsed {
    max-height: 0;
    padding: 0 20px;
    opacity: 0;
}

/* Order items */
.order-items {
    margin-bottom: 20px;
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child {
    border-bottom: none;
}

.item-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.item-name {
    font-weight: 500;
    color: #333;
}

.item-quantity {
    font-size: 0.9rem;
    color: #666;
}

.item-price {
    font-weight: 600;
    color: #8b4513;
}

/* Order totals */
.order-totals {
    border-top: 2px solid #ffc0cb;
    padding-top: 15px;
}

.subtotal-row,
.tax-row,
.shipping-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    color: #666;
}

.total-row {
    display: flex;
    justify-content: space-between;
    font-size: 1.2rem;
    font-weight: 600;
    color: #8b4513;
    padding-top: 10px;
    border-top: 1px solid #ddd;
}

/* Payment Section */
.payment-section {
    background-color: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    border: 2px solid #ffc0cb;
}

.payment-section h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #8b4513;
    margin-bottom: 25px;
}

/* Form styles */
.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #555;
}

input,
textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    font-family: inherit;
    transition: all 0.3s ease;
    background-color: #fafafa;
}

input:focus,
textarea:focus {
    outline: none;
    border-color: #ffc0cb;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(255, 192, 203, 0.1);
}

textarea {
    resize: vertical;
    min-height: 80px;
}

/* Error messages */
.error-message {
    display: block;
    color: #e74c3c;
    font-size: 0.875rem;
    margin-top: 4px;
    min-height: 1.2em;
}

/* Pay button */
.pay-button {
    width: 100%;
    background-color: #8b4513;
    color: white;
    border: none;
    padding: 16px 24px;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.pay-button:hover {
    background-color: #a0522d;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(139, 69, 19, 0.3);
}

.pay-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Modal styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: white;
    padding: 40px;
    border-radius: 16px;
    text-align: center;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    transform: scale(0.9);
    transition: transform 0.3s ease;
    border: 3px solid #ffc0cb;
}

.modal-overlay.show .modal-content {
    transform: scale(1);
}

.success-icon {
    font-size: 4rem;
    color: #27ae60;
    margin-bottom: 20px;
}

.modal-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #8b4513;
    margin-bottom: 15px;
}

.modal-content p {
    color: #666;
    margin-bottom: 10px;
}

.order-number {
    font-weight: 600;
    color: #8b4513;
    margin-bottom: 25px !important;
}

.modal-button {
    background-color: #ffc0cb;
    color: #8b4513;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modal-button:hover {
    background-color: #ffb6c1;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 192, 203, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
    .checkout-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .checkout-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .checkout-header h1 {
        font-size: 2rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .container {
        padding: 15px;
    }
    
    .payment-section {
        padding: 20px;
    }
    
    .modal-content {
        padding: 30px 20px;
    }

    .nav-content {
        padding: 0 15px;
    }
    
    .nav-left h3 {
        font-size: 1.3rem;
    }
    
    .real-time-clock {
        font-size: 0.8rem;
        padding: 6px 12px;
    }
}

@media (max-width: 480px) {
    .nav-space {
        height: 60px;
    }
    
    .checkout-header h1 {
        font-size: 1.8rem;
    }
    
    .back-to-cart-btn {
        padding: 10px 16px;
        font-size: 0.9rem;
    }
    
    .order-summary-header h2,
    .payment-section h2 {
        font-size: 1.3rem;
    }
}