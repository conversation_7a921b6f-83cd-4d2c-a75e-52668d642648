// DOM Elements
const loginBtn = document.getElementById('loginBtn');
const registerBtn = document.getElementById('registerBtn');
const loginForm = document.getElementById('loginForm');
const registerForm = document.getElementById('registerForm');
const googleLoginBtn = document.getElementById('googleLoginBtn');

// Password toggle functionality
const passwordToggles = document.querySelectorAll('.password-toggle');
passwordToggles.forEach(toggle => {
    toggle.addEventListener('click', function() {
        const input = this.parentElement.querySelector('input');
        const icon = this.querySelector('i');
        
        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
});

// Toggle between login and register forms
loginBtn.addEventListener('click', function() {
    if (!this.classList.contains('active')) {
        this.classList.add('active');
        registerBtn.classList.remove('active');
        loginForm.classList.add('active');
        registerForm.classList.remove('active');
        clearAllErrors();
    }
});

registerBtn.addEventListener('click', function() {
    if (!this.classList.contains('active')) {
        this.classList.add('active');
        loginBtn.classList.remove('active');
        registerForm.classList.add('active');
        loginForm.classList.remove('active');
        clearAllErrors();
    }
});

// Client-side validation functions
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validatePassword(password) {
    return password.length >= 6;
}

function validatePhone(phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

function validateUsername(username) {
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    return usernameRegex.test(username);
}

// Error display functions
function showError(inputId, message) {
    const errorElement = document.getElementById(inputId + 'Error');
    const inputElement = document.getElementById(inputId);
    
    if (errorElement && inputElement) {
        errorElement.textContent = message;
        errorElement.classList.add('show');
        inputElement.style.borderColor = '#d32f2f';
        inputElement.style.background = 'rgba(211, 47, 47, 0.05)';
    }
}

function clearError(inputId) {
    const errorElement = document.getElementById(inputId + 'Error');
    const inputElement = document.getElementById(inputId);
    
    if (errorElement && inputElement) {
        errorElement.textContent = '';
        errorElement.classList.remove('show');
        inputElement.style.borderColor = '#e8d5b0';
        inputElement.style.background = 'rgba(245, 230, 211, 0.3)';
    }
}

function clearAllErrors() {
    const errorElements = document.querySelectorAll('.error-message');
    const inputElements = document.querySelectorAll('input[type="text"], input[type="email"], input[type="password"], input[type="tel"]');
    
    errorElements.forEach(error => {
        error.textContent = '';
        error.classList.remove('show');
    });
    
    inputElements.forEach(input => {
        input.style.borderColor = '#e8d5b0';
        input.style.background = 'rgba(245, 230, 211, 0.3)';
    });
}

// Form validation before submission
loginForm.addEventListener('submit', function(e) {
    e.preventDefault(); // Prevent default form submission
    
    const email = document.getElementById('loginEmail').value.trim();
    const password = document.getElementById('loginPassword').value;
    
    let isValid = true;
    
    if (!email) {
        showError('loginEmail', 'Please enter your email');
        isValid = false;
    } else if (!validateEmail(email)) {
        showError('loginEmail', 'Please enter a valid email address');
        isValid = false;
    }
    
    if (!password) {
        showError('loginPassword', 'Please enter your password');
        isValid = false;
    }
    
    if (isValid) {
        // Create FormData object
        const formData = new FormData();
        formData.append('loginEmail', email);
        formData.append('loginPassword', password);
        
        // Call login function
        loginUser(formData);
    }
});

registerForm.addEventListener('submit', function(e) {
    e.preventDefault(); // Always prevent default submission
    
    const firstName = document.getElementById('firstName').value.trim();
    const lastName = document.getElementById('lastName').value.trim();
    const username = document.getElementById('username').value.trim();
    const email = document.getElementById('registerEmail').value.trim();
    const phone = document.getElementById('phone').value.trim();
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const agreeTerms = document.getElementById('agreeTerms').checked;
    
    let isValid = true;
    
    if (!firstName) {
        showError('firstName', 'Please enter your first name');
        isValid = false;
    } else if (firstName.length < 2) {
        showError('firstName', 'First name must be at least 2 characters');
        isValid = false;
    }
    
    if (!lastName) {
        showError('lastName', 'Please enter your last name');
        isValid = false;
    } else if (lastName.length < 2) {
        showError('lastName', 'Last name must be at least 2 characters');
        isValid = false;
    }
    
    if (!username) {
        showError('username', 'Please choose a username');
        isValid = false;
    } else if (!validateUsername(username)) {
        showError('username', 'Username must be 3-20 characters, letters, numbers, and underscores only');
        isValid = false;
    }
    
    if (!email) {
        showError('registerEmail', 'Please enter your email');
        isValid = false;
    } else if (!validateEmail(email)) {
        showError('registerEmail', 'Please enter a valid email address');
        isValid = false;
    }
    
    if (phone && !validatePhone(phone)) {
        showError('phone', 'Please enter a valid phone number');
        isValid = false;
    }
    
    if (!password) {
        showError('registerPassword', 'Please create a password');
        isValid = false;
    } else if (!validatePassword(password)) {
        showError('registerPassword', 'Password must be at least 6 characters long');
        isValid = false;
    }
    
    if (!confirmPassword) {
        showError('confirmPassword', 'Please confirm your password');
        isValid = false;
    } else if (password !== confirmPassword) {
        showError('confirmPassword', 'Passwords do not match');
        isValid = false;
    }
    
    if (!agreeTerms) {
        alert('Please agree to the Terms & Conditions to continue');
        isValid = false;
    }
    
    if (!isValid) {
        // Don't proceed if validation fails
        return;
    } else {
        console.log('Form is valid, submitting via API...');
        // Create FormData object
        const formData = new FormData();
        formData.append('firstName', firstName);
        formData.append('lastName', lastName);
        formData.append('username', username);
        formData.append('email', email);
        formData.append('phone', phone);
        formData.append('password', password);
        
        // Call the API registration function
        registerViaAPI(formData);
    }
});

// Real-time validation
const inputs = document.querySelectorAll('input');
inputs.forEach(input => {
    input.addEventListener('focus', function() {
        clearError(this.id);
    });
    
    input.addEventListener('blur', function() {
        if (this.value.trim() !== '') {
            validateSingleField(this.id, this.value);
        }
    });
});

function validateSingleField(fieldId, value) {
    switch(fieldId) {
        case 'loginEmail':
        case 'registerEmail':
            if (!validateEmail(value)) {
                showError(fieldId, 'Please enter a valid email address');
                return false;
            }
            break;
        case 'loginPassword':
        case 'registerPassword':
            if (!validatePassword(value)) {
                showError(fieldId, 'Password must be at least 6 characters long');
                return false;
            }
            break;
        case 'confirmPassword':
            const registerPassword = document.getElementById('registerPassword').value;
            if (value !== registerPassword) {
                showError(fieldId, 'Passwords do not match');
                return false;
            }
            break;
        case 'username':
            if (!validateUsername(value)) {
                showError(fieldId, 'Username must be 3-20 characters, letters, numbers, and underscores only');
                return false;
            }
            break;
        case 'phone':
            if (!validatePhone(value)) {
                showError(fieldId, 'Please enter a valid phone number');
                return false;
            }
            break;
        case 'firstName':
        case 'lastName':
            if (value.trim().length < 2) {
                showError(fieldId, 'Name must be at least 2 characters long');
                return false;
            }
            break;
    }
    return true;
}

// Google Login (simplified)
googleLoginBtn.addEventListener('click', function() {
    alert('Google login integration would be implemented here.');
});

// Check if user is already logged in
window.addEventListener('load', function() {
    const currentUser = localStorage.getItem('currentSnackAtlasUser');
    if (currentUser) {
        // User is already logged in, redirect to main page
        window.location.href = 'homepage.html';
    }
});

// Add smooth animations
document.addEventListener('DOMContentLoaded', function() {
    // Add entrance animation to the card
    const loginWrapper = document.querySelector('.login-wrapper');
    loginWrapper.style.opacity = '0';
    loginWrapper.style.transform = 'translateY(30px)';
    
    setTimeout(() => {
        loginWrapper.style.transition = 'all 0.6s ease-out';
        loginWrapper.style.opacity = '1';
        loginWrapper.style.transform = 'translateY(0)';
    }, 100);
});

// Add loading state to buttons
function addLoadingState(button) {
    const originalText = button.textContent;
    button.textContent = 'Loading...';
    button.disabled = true;
    
    setTimeout(() => {
        button.textContent = originalText;
        button.disabled = false;
    }, 2000);
}

// Add loading states to form submissions

// Update the registerViaAPI function
async function registerViaAPI(formData) {
    try {
        console.log('Sending registration data via FormData');
        
        // Show loading state
        const submitBtn = document.querySelector('#registerForm .submit-btn');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Creating account...';
        submitBtn.disabled = false;
        
        // Log the URL we're sending to
        console.log('Sending request to:', window.location.origin + '/register.php');
        
        // Send the data to the API with the correct path
        const response = await fetch('register.php', {
            method: 'POST',
            body: formData
        });
        
        // Log response status
        console.log('Response status:', response.status);
        
        const responseText = await response.text();
        console.log('Raw response:', responseText);
        
        let result;
        try {
            result = JSON.parse(responseText);
        } catch (e) {
            console.error('Failed to parse JSON response:', e);
            alert('Server returned an invalid response. Please try again later.');
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
            return;
        }
        
        console.log('Registration response:', result);
        
        if (result.success) {
            // Store user data in localStorage
            localStorage.setItem('currentSnackAtlasUser', JSON.stringify({
                id: result.userId,
                username: result.username,
                firstName: result.firstName,
                lastName: result.lastName,
                email: result.email
            }));
            
            // Show success message
            const successMessage = document.getElementById('successMessage');
            const successText = document.getElementById('successText');
            successText.textContent = result.message;
            successMessage.style.display = 'flex';
            
            // Redirect after a short delay
            setTimeout(() => {
                window.location.href = 'homepage.html';
            }, 2000);
        } else {
            // Show error message
            alert(result.message || 'Registration failed');
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    } catch (error) {
        console.error('Registration error:', error);
        alert('An error occurred during registration: ' + error.message);
        
        // Reset button state
        const submitBtn = document.querySelector('#registerForm .submit-btn');
        submitBtn.textContent = 'Create Account';
        submitBtn.disabled = false;
    }
}

// Function to handle user login
async function loginUser(formData) {
    try {
        // Show loading state
        const submitBtn = document.querySelector('#loginForm .submit-btn');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Signing in...';
        submitBtn.disabled = true;
        
        // Send login request
        const response = await fetch('login.php', {
            method: 'POST',
            body: formData
        });
        
        const responseText = await response.text();
        console.log('Raw login response:', responseText);
        
        let result;
        try {
            result = JSON.parse(responseText);
        } catch (e) {
            console.error('Failed to parse JSON response:', e);
            alert('Server returned an invalid response. Please try again later.');
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
            return;
        }
        
        console.log('Login response:', result);
        
        if (result.success) {
            // Store user data in localStorage
            localStorage.setItem('currentSnackAtlasUser', JSON.stringify({
                id: result.userId,
                username: result.username,
                firstName: result.firstName,
                lastName: result.lastName,
                email: result.email
            }));
            
            // Show success message
            const successMessage = document.getElementById('successMessage');
            const successText = document.getElementById('successText');
            successText.textContent = 'Login successful! Redirecting...';
            successMessage.style.display = 'flex';
            
            // Redirect to homepage after a short delay
            setTimeout(() => {
                window.location.href = 'homepage.html';
            }, 1500);
        } else {
            // Show error message
            alert(result.message || 'Login failed');
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    } catch (error) {
        console.error('Login error:', error);
        alert('An error occurred during login: ' + error.message);
        
        // Reset button state
        const submitBtn = document.querySelector('#loginForm .submit-btn');
        submitBtn.textContent = 'Sign In';
        submitBtn.disabled = false;
    }
}
