<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

$host = 'localhost';
$dbname = 'group4_db';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // First check if the cart table exists and has items
    $checkQuery = "SELECT COUNT(*) FROM cart";
    $count = $pdo->query($checkQuery)->fetchColumn();
    
    if ($count > 0) {
        // Get cart items with stored product details
        $query = "SELECT
                    c.product_id as id,
                    c.product_name as name,
                    p.description,
                    CAST(c.product_price AS FLOAT) as price,
                    c.product_image as image,
                    c.quantity
                FROM cart c
                LEFT JOIN products p ON c.product_id = p.id
                ORDER BY c.added_at DESC";
        $stmt = $pdo->query($query);
        $cartItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } else {
        // No items in cart
        $cartItems = [];
    }

    echo json_encode([
        'success' => true,
        'cart' => $cartItems
    ]);
} catch (PDOException $e) {
    // Log the error for debugging
    error_log("Database error in get-cart.php: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>

