@import url("https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700;800;900&display=swap");
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: #e6ccb2;
    border-radius: 6px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #582f0e 0%, #8b4513 100%);
    border-radius: 6px;
    border: 2px solid #e6ccb2;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #8b4513 0%, #582f0e 100%);
}

body {
    font-family: 'Poppins', sans-serif;
    background: white;
    overflow-x: hidden;
}

.container {
    position: relative;
    width: 100vw;
    min-height: 100vh;
    background: white;
}

.content-wrapper {
    width: 100%;
    position: relative;
    padding: 0 20px;
    z-index: 5;
}

/* Background Images */
.bg-image {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    object-fit: cover;
    z-index: 1;
}

/* Header */
.header {
    position: relative;
    z-index: 10;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 46px 80px 0;
    width: 100%;
}

.nav-left .logo {
    width: 53px;
    height: 49px;
    border-radius: 8px;
}

.nav-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.nav-links {
    display: flex;
    align-items: center;
    list-style: none;
    gap: 0;
}

.nav-link {
    font-family: 'Poppins', sans-serif;
    color: #582f0e;
    font-size: 24px;
    text-decoration: none;
    font-weight: 600;
    letter-spacing: 0.48px;
    transition: all 0.3s ease;
}

.nav-link:hover {
    font-weight: 700;
    text-decoration: underline;
    transform: translateY(-2px);
}

.nav-link.active {
    font-weight: 700;
    text-decoration: underline;
}

.nav-separator {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: #582f0e;
    font-size: 24px;
    margin: 0 8px;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.nav-icon {
    width: 28px;
    height: 24px;
    color: #582f0e;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.nav-icon:hover {
    transform: scale(1.1);
}

.clock {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: #582f0e;
    font-size: 24px;
    letter-spacing: 0.24px;
    min-width: 120px;
    text-align: center;
}

/* Search Container */
.search-container {
    display: flex;
    justify-content: center;
    margin: 74px 0 0;
}

.search-bar {
    width: min(1117px, 90%);
    height: 90px;
    background: rgba(207, 207, 207, 0.65);
    border: 3px solid #582f0e;
    border-radius: 30px;
    display: flex;
    align-items: center;
    padding: 0 38px;
    backdrop-filter: blur(2.15px);
    position: relative;
}

.search-icon {
    width: 39px;
    height: 36px;
    color: #582f0e;
    margin-right: 20px;
}

.search-input {
    flex: 1;
    background: transparent;
    border: none;
    outline: none;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 24px;
    color: #582f0e;
    letter-spacing: 0.36px;
    max-width: 284px;
}

.search-input::placeholder {
    color: #e6ccb2;
    -webkit-text-stroke: 1.1px #000000;
}

/* Filter Styles */
.filter-section {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: auto;
}

.filter-icon {
    width: 24px;
    height: 24px;
    color: #582f0e;
}

.filter-dropdown {
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid #582f0e;
    border-radius: 15px;
    padding: 8px 12px;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    font-size: 16px;
    color: #582f0e;
    cursor: pointer;
    outline: none;
    transition: all 0.2s ease;
    min-width: 180px;
}

.filter-dropdown:hover, 
.filter-dropdown:focus {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 8px rgba(88, 47, 14, 0.2);
}

.filter-dropdown option {
    background: white;
    color: #582f0e;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
}

/* Page Title */
.page-title {
    font-family: 'Playfair Display', serif;
    font-weight: 800;
    font-size: 100px;
    color: #e6ccb2;
    -webkit-text-stroke: 2px #000000;
    text-align: center;
    margin: 9px 0 61px;
    letter-spacing: 0;
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(366px, 1fr));
    gap: 30px;
    margin: 0 auto;
    max-width: 1200px;
    min-height: 500px;
    justify-items: center;
}

/* Product Card */
.product-card {
    width: 366px;
    height: 500px;
    background: rgba(207, 207, 207, 0.65);
    border: 3px solid #582f0e;
    border-radius: 30px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(2.15px);
    overflow: visible; /* Ensure the heart button is not cut off */
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(88, 47, 14, 0.3);
    background: rgba(207, 207, 207, 0.8);
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

/* Heart Button Styles */
.heart-btn {
    position: absolute;
    top: 17px;
    right: 17px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    z-index: 10;
    transition: transform 0.2s ease;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.7);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.heart-btn:hover {
    transform: scale(1.2);
    background-color: rgba(255, 255, 255, 0.9);
}

.heart-icon {
    width: 24px;
    height: 24px;
    fill: none;
    stroke: #582f0e;
    stroke-width: 2;
    transition: all 0.2s ease;
    display: block;
}

.heart-icon.liked {
    fill: #ff4757;
    stroke: #ff4757;
}

/* Ensure the heart button is visible on hover */
.product-card:hover .heart-btn {
    opacity: 1;
}

.product-image-container {
    display: flex;
    justify-content: center;
    margin-top: 43px;
    height: 231px;
    overflow: hidden;
}

.product-image {
    max-width: 259px;
    max-height: 231px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.product-info {
    position: absolute;
    width: 320px;
    top: 284px;
    left: 22px;
    text-align: center;
}

.product-name {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 24px;
    color: #e6ccb2;
    -webkit-text-stroke: 1.5px #000000;
    letter-spacing: 0.09px;
    margin-bottom: 10px;
}

.product-divider {
    font-size: 22px;
    color: #e6ccb2;
    -webkit-text-stroke: 1.5px #000000;
    letter-spacing: 0.07px;
    margin: 10px 0;
}

.product-description {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 20px;
    color: #e6ccb2;
    -webkit-text-stroke: 1.5px #000000;
    letter-spacing: 0.06px;
    line-height: 1.2;
    margin-bottom: 20px;
}

.product-price {
    position: absolute;
    bottom: 36px;
    left: 50%;
    transform: translateX(-50%);
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 24px;
    color: #582f0e;
    text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    letter-spacing: 0.36px;
}

/* Enhanced Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 40px 0;
    font-family: 'Poppins', sans-serif;
}

.pagination-arrow {
    background: rgba(207, 207, 207, 0.8);
    border: 2px solid #582f0e;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #582f0e;
}

.pagination-arrow:hover:not(:disabled) {
    background: #c4956c;
    color: white;
    transform: scale(1.1);
}

.pagination-arrow:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: 5px;
    align-items: center;
}

.page-number {
    background: rgba(207, 207, 207, 0.8);
    border: 2px solid #582f0e;
    border-radius: 8px;
    padding: 8px 12px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 16px;
    color: #582f0e;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 40px;
    text-align: center;
}

.page-number:hover {
    background: #c4956c;
    color: white;
    transform: translateY(-2px);
}

.page-number.active {
    background: #582f0e;
    color: white;
}

.page-ellipsis {
    color: #582f0e;
    font-weight: 600;
    padding: 0 5px;
}

/* Loading Indicator */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    font-family: 'Poppins', sans-serif;
    color: #e6ccb2;
    font-size: 18px;
    -webkit-text-stroke: 1px #000000;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #e6ccb2;
    border-top: 2px solid #582f0e;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    font-family: 'Poppins', sans-serif;
    color: #d32f2f;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 20px;
    margin: 20px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    position: relative;
    background: rgba(230, 204, 178, 0.95);
    border: 3px solid #582f0e;
    border-radius: 30px;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    z-index: 5;
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    background: #c4956c;
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    font-size: 20px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
    z-index: 10;
}

.modal-close:hover {
    background: #a67c52;
}

.modal-body {
    display: flex;
    padding: 30px;
    gap: 30px;
}

.modal-image {
    flex: 0 0 40%;
}

.modal-image img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    object-fit: cover;
    border: 2px solid #582f0e;
}

.modal-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.modal-product-name {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    font-size: 28px;
    color: #582f0e;
    margin-bottom: 5px;
}

.modal-status, .modal-price {
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    color: #582f0e;
}

.status-badge {
    background: #4caf50;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
}

.status-badge.unavailable {
    background: #f44336;
}

.modal-price {
    font-size: 20px;
    font-weight: 600;
}

.modal-section {
    margin-top: 10px;
}

.modal-section h3 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 18px;
    color: #582f0e;
    margin-bottom: 5px;
}

.modal-section p {
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
    font-size: 16px;
    color: #333;
    line-height: 1.5;
}

.quantity-section {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 20px;
}

.quantity-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: #c4956c;
    color: white;
    border: none;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 0.2s ease;
}

.quantity-btn:hover {
    background: #a67c52;
}

.quantity-btn:disabled {
    background: #d1c0a8;
    cursor: not-allowed;
}

.quantity {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 20px;
    color: #582f0e;
}

.add-to-cart-btn {
    background: #582f0e;
    color: white;
    border: none;
    border-radius: 30px;
    padding: 12px 24px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.2s ease;
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.add-to-cart-btn:hover {
    background: #8b4513;
}

/* User Modal Styles */
.user-modal .modal-content {
    max-width: 900px;
    width: 90%;
}

.user-modal-body {
    padding: 30px;
}

.user-modal-body h2 {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    font-size: 32px;
    color: #582f0e;
    text-align: center;
    margin-bottom: 30px;
}

.user-tabs {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.tab-btn {
    background: rgba(196, 149, 108, 0.3);
    border: 2px solid #582f0e;
    border-radius: 20px;
    padding: 12px 24px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 16px;
    color: #582f0e;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background: #c4956c;
    color: white;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.tab-content h3 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 24px;
    color: #582f0e;
    margin-bottom: 20px;
}

/* Profile Section */
.profile-section {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.profile-picture {
    text-align: center;
}

.profile-picture img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #582f0e;
    margin-bottom: 15px;
}

.upload-btn {
    background: #c4956c;
    border: none;
    border-radius: 15px;
    padding: 8px 16px;
    color: white;
    cursor: pointer;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
}

.profile-info {
    flex: 1;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #582f0e;
    margin-bottom: 5px;
}

.form-input {
    width: 100%;
    padding: 10px;
    border: 2px solid #582f0e;
    border-radius: 10px;
    font-family: 'Poppins', sans-serif;
    background: rgba(255, 255, 255, 0.8);
}

.save-btn {
    background: #582f0e;
    color: white;
    border: none;
    border-radius: 15px;
    padding: 12px 24px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s ease;
}

.save-btn:hover {
    background: #8b4513;
}

/* Wishlist Grid */
.saved-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}

.saved-product-item {
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid #582f0e;
    border-radius: 15px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.saved-product-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(88, 47, 14, 0.2);
}

.saved-product-item img {
    width: 100%;
    height: 120px;
    object-fit: contain;
    border-radius: 10px;
    margin-bottom: 10px;
}

.saved-product-item h4 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 16px;
    color: #582f0e;
    margin-bottom: 5px;
}

.saved-product-item p {
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    font-size: 14px;
    color: #8b5a3c;
}

/* Settings Section */
.settings-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.settings-btn {
    background: #c4956c;
    color: white;
    border: none;
    border-radius: 15px;
    padding: 12px 24px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s ease;
}

.settings-btn:hover {
    background: #a67c52;
}

.delete-btn {
    background: #f44336;
    color: white;
    border: none;
    border-radius: 15px;
    padding: 12px 24px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s ease;
}

.delete-btn:hover {
    background: #d32f2f;
}

/* Order History */
.order-history {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.order-item {
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid #582f0e;
    border-radius: 15px;
    padding: 20px;
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.order-id {
    font-weight: 600;
    color: #582f0e;
}

.order-date {
    color: #8b5a3c;
    font-size: 14px;
}

.order-total {
    font-weight: 600;
    color: #582f0e;
    font-size: 18px;
}

/* No products message */
.no-products {
    text-align: center;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 24px;
    color: #582f0e;
    margin: 60px 0;
    grid-column: 1 / -1;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        margin: 0 50px;
    }
    
    .header {
        padding: 46px 40px 0;
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 20px;
        padding: 20px;
    }
    
    .nav-center {
        position: static;
        transform: none;
    }
    
    .nav-links {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .nav-link {
        font-size: 20px;
    }
    
    .page-title {
        font-size: 60px;
    }
    
    .products-grid {
        grid-template-columns: 1fr;
        margin: 0 20px;
    }
    
    .product-card {
        width: 100%;
        max-width: 366px;
        margin: 0 auto;
    }
    
    .modal-body {
        flex-direction: column;
        padding: 20px;
    }
    
    .modal-image {
        flex: none;
    }
    
    .profile-section {
        flex-direction: column;
        text-align: center;
    }
    
    .user-tabs {
        flex-direction: column;
        align-items: center;
    }
}
