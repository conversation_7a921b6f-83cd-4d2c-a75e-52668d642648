/* style.css */

body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(to right, #bca98f, #a9927d);
    margin: 0;
    padding: 0;
    color: #3b2f2f;
    text-align: center;
}

h1 {
    text-align: center;
    font-size: 2.5em;
    margin-top: 1em;
    color: #3b2f2f;
}

button, input[type="submit"] {
    background-color: #5e4b3c;
    color: #fff;
    border: none;
    padding: 10px 20px;
    margin: 10px 0;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    font-size: 1em;
    transition: background-color 0.3s;
}

button:hover, input[type="submit"]:hover {
    background-color: #fce8d7;
    color: #5e4b3c;
}

a {
    text-decoration: none;
    margin: 10px;
}

form {
    background-color: #fff3e0;
    align-content: center;
    max-width: 500px;
    margin: 2em auto;
    padding: 2em;
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
}

input[type="text"],
input[type="number"],
input[type="file"],
textarea {
    width: 100%;
    padding: 10px;
    margin-top: 10px;
    margin-bottom: 15px;
    border: 1px solid #a1887f;
    border-radius: 6px;
    box-sizing: border-box;
    font-size: 1em;
}

table {
    width: 90%;
    margin: 2em auto;
    border-collapse: collapse;
    background-color: #fffaf0;
}

table th, table td {
    border: 1px solid #d7ccc8;
    padding: 12px;
    text-align: center;
}

table th {
    background-color: #8d6e63;
    color: white;
    font-weight: bold;
}

img {
    border-radius: 6px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}
