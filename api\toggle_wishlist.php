<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// Database configuration
$host = 'localhost';
$dbname = 'group4_db';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    $userId = $input['user_id'] ?? null;
    $productId = $input['product_id'] ?? null;
    $action = $input['action'] ?? null;
    
    if (!$userId || !$productId || !$action) {
        echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
        exit;
    }
    
    // Check if wishlist table exists, create if not
    $checkTable = $pdo->query("SHOW TABLES LIKE 'wishlist'");
    if ($checkTable->rowCount() == 0) {
        // Create wishlist table
        $createTable = "CREATE TABLE `wishlist` (
            `id` int NOT NULL AUTO_INCREMENT,
            `user_id` int NOT NULL,
            `product_id` varchar(20) NOT NULL,
            `added_at` datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_user_product` (`user_id`, `product_id`),
            INDEX `idx_user_id` (`user_id`),
            INDEX `idx_product_id` (`product_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createTable);
    }
    
    if ($action === 'add') {
        // Add item to wishlist
        $sql = "INSERT INTO wishlist (user_id, product_id) VALUES (?, ?) 
                ON DUPLICATE KEY UPDATE added_at = CURRENT_TIMESTAMP";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$userId, $productId]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Added to wishlist',
            'action' => 'added'
        ]);
        
    } elseif ($action === 'remove') {
        // Remove item from wishlist
        $sql = "DELETE FROM wishlist WHERE user_id = ? AND product_id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$userId, $productId]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Removed from wishlist',
            'action' => 'removed'
        ]);
        
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
    }
    
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>
