-- Updated cart table structure to include product image and price
-- This allows the cart to store product information directly for better performance
-- and to handle cases where product details might change after being added to cart

CREATE TABLE `cart` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` varchar(20) NOT NULL,
  `product_name` varchar(100) NOT NULL,
  `product_image` varchar(255) DEFAULT NULL,
  `product_price` decimal(10,2) NOT NULL,
  `quantity` int NOT NULL DEFAULT '1',
  `added_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_product_id` (`product_id`),
  FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Alternative approach: If you prefer to always fetch fresh data from products table
-- You can use a JOIN query instead of storing redundant data in cart table
-- This keeps the cart table minimal but requires JOIN operations for display

-- Minimal cart table (alternative approach):
/*
CREATE TABLE `cart_minimal` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` varchar(20) NOT NULL,
  `quantity` int NOT NULL DEFAULT '1',
  `added_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_product_id` (`product_id`),
  FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
*/

-- Sample query to fetch cart items with product details (for minimal approach):
/*
SELECT 
    c.id as cart_id,
    c.product_id,
    c.quantity,
    c.added_at,
    p.name as product_name,
    p.description,
    p.price as product_price,
    p.image as product_image,
    p.category,
    (p.price * c.quantity) as total_price
FROM cart_minimal c
JOIN products p ON c.product_id = p.id
ORDER BY c.added_at DESC;
*/

-- Sample queries for the updated cart table with stored product info:

-- Insert item into cart (storing product details)
/*
INSERT INTO cart (product_id, product_name, product_image, product_price, quantity)
SELECT id, name, image, price, 1
FROM products 
WHERE id = 'PRODUCT_ID_HERE';
*/

-- Update quantity in cart
/*
UPDATE cart 
SET quantity = quantity + 1 
WHERE product_id = 'PRODUCT_ID_HERE';
*/

-- Get all cart items
/*
SELECT 
    id as cart_id,
    product_id,
    product_name,
    product_image,
    product_price,
    quantity,
    (product_price * quantity) as total_price,
    added_at
FROM cart 
ORDER BY added_at DESC;
*/

-- Get cart summary
/*
SELECT 
    COUNT(*) as total_items,
    SUM(quantity) as total_quantity,
    SUM(product_price * quantity) as subtotal
FROM cart;
*/

-- Remove item from cart
/*
DELETE FROM cart WHERE product_id = 'PRODUCT_ID_HERE';
*/

-- Clear entire cart
/*
DELETE FROM cart;
*/
