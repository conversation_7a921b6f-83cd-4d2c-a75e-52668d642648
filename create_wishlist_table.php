<?php
// Create wishlist table
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "group4_db";

try {
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    
    echo "<h1>Creating Wishlist Table</h1>";
    
    $sql = "CREATE TABLE IF NOT EXISTS `wishlist` (
        `id` int NOT NULL AUTO_INCREMENT,
        `user_id` int NOT NULL,
        `product_id` varchar(20) NOT NULL,
        `added_at` datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `unique_user_product` (`user_id`, `product_id`),
        INDEX `idx_user_id` (`user_id`),
        INDEX `idx_product_id` (`product_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($sql) === TRUE) {
        echo "<p>✓ Wishlist table created successfully</p>";
    } else {
        echo "<p>✗ Error creating table: " . $conn->error . "</p>";
    }
    
    // Check if table exists and show structure
    $result = $conn->query("DESCRIBE wishlist");
    if ($result) {
        echo "<h2>Table Structure:</h2>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px 12px; border: 1px solid #ddd; }
th { background-color: #f5f5f5; }
</style>
