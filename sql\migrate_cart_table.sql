-- Migration script to update cart table structure
-- This script will add the missing columns to the existing cart table

-- First, let's check the current structure and add missing columns
-- Run this step by step to avoid data loss

-- Step 1: Add new columns to existing cart table
ALTER TABLE `cart` 
ADD COLUMN `product_image` varchar(255) DEFAULT NULL AFTER `product_name`,
ADD COLUMN `product_price` decimal(10,2) NOT NULL DEFAULT 0.00 AFTER `product_image`;

-- Step 2: Add proper indexes for better performance
ALTER TABLE `cart` 
ADD INDEX `idx_product_id` (`product_id`),
ADD INDEX `idx_added_at` (`added_at`);

-- Step 3: Update existing cart records with product information
-- This will populate the new columns with data from the products table
UPDATE cart c 
JOIN products p ON c.product_id = p.id 
SET 
    c.product_image = p.image,
    c.product_price = p.price
WHERE c.product_image IS NULL OR c.product_price = 0.00;

-- Step 4: Add foreign key constraint (optional - only if you want referential integrity)
-- Uncomment the following line if you want to enforce foreign key relationships
-- ALTER TABLE `cart` ADD FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE;

-- Step 5: Verify the migration
-- Run this query to check if the migration was successful
/*
SELECT 
    c.id,
    c.product_id,
    c.product_name,
    c.product_image,
    c.product_price,
    c.quantity,
    c.added_at,
    (c.product_price * c.quantity) as total_price
FROM cart c
ORDER BY c.added_at DESC;
*/

-- Alternative: If you prefer to recreate the table (WARNING: This will delete all cart data)
/*
-- Backup existing cart data first
CREATE TABLE cart_backup AS SELECT * FROM cart;

-- Drop and recreate the table
DROP TABLE cart;

CREATE TABLE `cart` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` varchar(20) NOT NULL,
  `product_name` varchar(100) NOT NULL,
  `product_image` varchar(255) DEFAULT NULL,
  `product_price` decimal(10,2) NOT NULL,
  `quantity` int NOT NULL DEFAULT '1',
  `added_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_product_id` (`product_id`),
  INDEX `idx_added_at` (`added_at`),
  FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Restore data from backup (you'll need to populate the new columns manually)
INSERT INTO cart (product_id, product_name, quantity, added_at)
SELECT product_id, product_name, quantity, added_at FROM cart_backup;

-- Update with product details
UPDATE cart c 
JOIN products p ON c.product_id = p.id 
SET 
    c.product_image = p.image,
    c.product_price = p.price;

-- Drop backup table
DROP TABLE cart_backup;
*/
