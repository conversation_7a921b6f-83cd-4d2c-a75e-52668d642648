<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

$host = 'localhost';
$dbname = 'group4_db';
$username = 'root';
$password = '';

try {
    // Get JSON data
    $input = file_get_contents("php://input");
    $data = json_decode($input, true);
    
    if (!$data || !isset($data['product_id']) || !isset($data['quantity'])) {
        throw new Exception('Invalid data received');
    }
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $productId = $data['product_id'];
    $quantity = (int)$data['quantity'];
    
    // Update cart quantity
    $stmt = $pdo->prepare("UPDATE cart SET quantity = ? WHERE product_id = ?");
    $result = $stmt->execute([$quantity, $productId]);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Cart updated successfully'
        ]);
    } else {
        throw new Exception("Failed to update cart");
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
?>